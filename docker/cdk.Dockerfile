FROM --platform=linux/amd64 python:3.10-slim

WORKDIR /app

# Install Poetry
RUN apt-get update \
    && apt-get upgrade -y \
    && apt-get install gcc g++ curl build-essential postgresql-server-dev-all -y \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*
RUN curl -sSL https://install.python-poetry.org | python3 -
# # Add Poetry to PATH
ENV PATH="${PATH}:/root/.local/bin"
# # Copy the pyproject.toml and poetry.lock files
COPY poetry.lock pyproject.toml ./
# Copy the rest of the application codes
COPY ./ ./

# Install dependencies
RUN poetry config virtualenvs.create false && poetry install --no-interaction --no-ansi

RUN poetry add botocore
RUN poetry add pymysql

CMD ["sh", "./container-cmd-cdk.sh"]
