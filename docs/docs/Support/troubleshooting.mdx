---
title: Troubleshoot Langflow
slug: /troubleshoot
---

import Tabs from '@theme/Tabs';
import TabI<PERSON> from '@theme/TabItem';

This page provides troubleshooting advice for issues you might encounter when using Langflow or contributing to Langflow.

## Missing components

As Langflow development continues, components are often recategorized or deprecated for better alignment or to prepare for new components.

If a component appears to be missing from the expected location on the **Components** menu, try the following:

* Search for the component or check other component categories, including [**Bundles**](/components-bundle-components).
* [Expose legacy components](/concepts-components#component-menus), which are hidden by default.
* Check the [Changelog](https://github.com/langflow-ai/langflow/releases/latest) for component changes in recent releases.
* Make sure the component isn't already present in your flow if it is a single-use component.

If you still cannot locate the component, see [Langflow GitHub Issues and Discussions](/contributing-github-issues).

## No input in the Playground

If there is no message input field in the **Playground**, make sure your flow has a [**Chat Input** component](/components-io) that is connected, directly or indirectly, to the **Input** port of a **Language Model** or **Agent** component.

Because the **Playground** is designed for flows that use an LLM in a query-and-response format, such as chatbots and agents, a flow must have **Chat Input**, **Language Model**/**Agent**, and **Chat Output** components to be fully supported by the **Playground**'s chat interface.

For more information, see [Use the **Playground**](/concepts-playground).

## Missing key, no key found, or invalid API key

If you get an API key error when running a flow, try the following:

* For all components that require credentials, make sure those components have a valid credential in the component's settings, such as the **API key** field.
* If you store your credentials in [Langflow global variables](/configuration-global-variables), make sure you selected the correct global variable and that the variable contains a valid credential.
* Make sure the provided credentials are active, have the required permissions, and, if applicable, have sufficient funds in the account to execute the required action. For example, model providers require credits to use their LLMs.

## Langflow installation issues

The following issues can occur when installing Langflow.

### Langflow installation freezes at pip dependency resolution

Installing Langflow OSS with `pip install langflow` slowly fails with this error message:

```text
pip is looking at multiple versions of <<library>> to determine which version is compatible with other requirements. This could take a while.
```

To work around this issue, install Langflow with `uv` instead of `pip`, as explained in [Install and run the Langflow OSS Python package](/get-started-installation#install-and-run-the-langflow-oss-python-package).

### Linux installation fails to build required package

When you try to install Langflow OSS on Linux, installation fails because of outdated or missing packages:

```bash
Resolved 455 packages in 18.92s
  × Failed to build `webrtcvad==2.0.10`
  ├─▶ The build backend returned an error
  ╰─▶ Call to `setuptools.build_meta:__legacy__.build_wheel` failed (exit status: 1)
```

To resolve this error, install the required build dependencies, and then retry the Langflow installation:

```bash
sudo apt-get update
sudo apt-get install build-essential python3-dev
```

If upgrading your packages doesn't fix the issue, install `gcc` separately, and then retry the Langflow installation:

```bash
sudo apt-get install gcc
```

### Installation failure from `webrtcvad` package

If you experience an error from the `webrtcvad` package, run `uv pip install webrtcvad-wheels` in your virtual environment, and then retry the Langflow installation.

### C++ build tools required for Langflow Desktop on Windows

Microsoft Windows installations of Langflow Desktop require a C++ compiler that may not be present on your system. If you receive a `C++ Build Tools Required!` error, follow the on-screen prompt to install Microsoft C++ Build Tools, or [install Microsoft Visual Studio](https://visualstudio.microsoft.com/downloads/).

## Langflow startup issues

The following issues can occur when attempting to start Langflow.

### No `langflow.__main__` module

When you try to run Langflow with the command `langflow run`, you encounter the following error:

```bash
> No module named 'langflow.__main__'
```

To resolve this issue, try the following:

1. Run `uv run langflow run` instead of `langflow run`.
2. If that doesn't work, reinstall the latest Langflow version with `uv pip install langflow -U`.
3. If that doesn't work, reinstall Langflow and its dependencies with `uv pip install langflow --pre -U --force-reinstall`.

### Langflow runTraceback

When you try to run Langflow using the command `langflow run`, you encounter the following error:

```bash
> langflow runTraceback (most recent call last): File ".../langflow", line 5, in <module>  from langflow.__main__ import mainModuleNotFoundError: No module named 'langflow.__main__'
```

There are two possible reasons for this error:

* **Multiple Langflow installations**: You installed Langflow using `pip install langflow` but you already had a previous version of Langflow installed in your system. In this case, you might be running the wrong executable.

    To solve this issue, run the correct executable by running `python -m langflow run` instead of `langflow run`.

    If that doesn't work, try uninstalling and reinstalling Langflow with `uv pip install langflow --pre -U`.

* **Version conflict during installation**: Some version conflicts might have occurred during the installation process. To resolve this issue, reinstall Langflow and its dependencies by running `python -m pip install langflow --pre -U --force-reinstall`.

### Environment variables not available from terminal

Environment variables set in your terminal are not automatically available to GUI-based applications like Langflow Desktop when launched through the Finder or the Start Menu.
To set environment variables for Langflow Desktop, see [Set environment variables for Langflow Desktop](/environment-variables#set-environment-variables-for-langflow-desktop).

### Package is not installed

In Langflow OSS, you can follow the error message's instructions to install the missing dependency.

To manage dependencies in Langflow Desktop, see [Install custom dependencies in Langflow Desktop](/install-custom-dependencies#langflow-desktop).

## Langflow upgrade issues

The following issues can occur when upgrading your Langflow version.

For information about managing Langflow versions, see [Install Langflow](/get-started-installation).

### Something went wrong running migrations

The following error can occur during Langflow upgrades when the new version can't override `langflow-pre.db` in the Langflow cache folder:

```bash
> Something went wrong running migrations. Please, run 'langflow migration --fix'
```

To resolve this error, clear the cache by deleting the contents of the Langflow cache folder.

:::important
Clearing the cache erases your settings.
If you want to retain your settings files, create a backup of those files before clearing the cache folder.
:::

The cache folder location depends on your OS:

- **Linux**: `home/<username>/.cache/langflow/`
- **WSL2 on Windows**: `home/<username>/.cache/langflow/`
- **macOS**: `/Users/<USER>/Library/Caches/langflow/`

## Langflow uninstall issues

The following issues can occur when uninstalling Langflow.

### Dot directory isn't deleted when uninstalling Langflow Desktop on macOS

On macOS, uninstalling Langflow Desktop deletes the `.app` file but doesn't delete files in `~/.langflow`, which includes files generated during usage like cache and settings.

If you reinstall Langflow Desktop, it starts with the existing data from the previous installation.

To fully remove a Langflow Desktop macOS installation, you must also delete `~/.langflow`:

    ```bash
    rm -rf .langflow
    ```

## MCP server issues

- [Use MCP Inspector to test and debug flows](/mcp-server#test-and-debug-flows)
- [Troubleshooting MCP server](/mcp-server#troubleshooting-mcp-server)

## Custom components and integrations issues

For troubleshooting advice for a third-party integration, see the information about that integration in the Langflow documentation and the provider's documentation.

If you are building a custom component, see [Error handling and logging for custom Python components](/components-custom-components#error-handling-and-logging).

## See also

- [Langflow GitHub Issues and Discussions](/contributing-github-issues)
- [Langflow telemetry](/contributing-telemetry)