---
title: Logs
slug: /logging
---

import Icon from "@site/src/components/icon";

This page provides information about Langflow logs, including logs for individual flows and the Langflow application itself.

## Log options

Langflow uses the `loguru` library for logging.

The default `log_level` is `ERROR`. Other options are `DEBUG`, `INFO`, `WARNING`, and `CRITICAL`.

The default logfile is called `langflow.log`, and its location depends on your operating system.

- **macOS Desktop**:`/Users/<USER>/.langflow/cache`
- **Windows Desktop**:`C:\Users\<USER>\AppData\Roaming\com.Langflow\cache`
- **OSS macOS/Windows/Linux/WSL (uv pip install)**: `<path_to_venv>/lib/python3.12/site-packages/langflow/cache`
- **OSS macOS/Windows/Linux/WSL (git clone)**: `<path_to_clone>/src/backend/base/langflow/cache`

The `LANGFLOW_LOG_ENV` controls log output and formatting. The `container` option outputs serialized JSON to stdout. The `container_csv` option outputs CSV-formatted plain text to stdout. The `default` (or not set) logging option outputs prettified output with [RichHandler](https://rich.readthedocs.io/en/stable/reference/logging.html).

To modify Langflow's logging configuration, add them to your `.env` file and start Langflow.

```text
LANGFLOW_LOG_LEVEL=ERROR
LANGFLOW_LOG_FILE=path/to/logfile.log
LANGFLOW_LOG_ENV=container
```

To start Langflow with the values from your .env file, start Langflow with `uv run langflow run --env-file .env`

## Flow and component logs

After you run a flow, you can inspect the logs for the each component and flow run.
For example, you can inspect `Message` objects ingested and generated by [input and output components](/components-io).

### View flow logs

In the visual editor, click **Logs** to view logs for the entire flow:

![Logs pane](/img/logs.png)

Then, click the cells in the **inputs** and **outputs** columns to inspect the `Message` objects.
For example, the following `Message` data could be the output from a **Chat Input** component:

```text
    "messages": [
    {
        "message": "What's the recommended way to install Docker on Mac M1?",
        "sender": "User",
        "sender_name": "User",
        "session_id": "Session Apr 21, 17:37:04",
        "stream_url": null,
        "component_id": "ChatInput-4WKag",
        "files": [],
        "type": "text"
    }
    ],
```

In the case of Input/Output components, the original input might not be structured as a `Message` object.
For example, a **Language Model** component might pass a raw text response to a **Chat Output** component that is then transformed into a `Message` object.

### View chat logs

In the **Playground**, you can inspect the chat history for each chat session.
For more information, see [Use the Playground](/concepts-playground).

### View output from a single component

When debugging issues with the format or content of a flow's output, it can help to inspect each component's output to determine where data is being lost or malformed.

To view the output produced by a single component during the most recent run, click <Icon name="TextSearch" aria-hidden="true"/> **Inspect output** in the visual editor.