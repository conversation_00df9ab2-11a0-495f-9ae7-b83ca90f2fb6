---
title: About developing and configuring Langflow applications
slug: /develop-overview
---

The following pages provide information about how to develop and configure Langflow applications.

The [Develop an application in Langflow](/develop-application) guide walks you through packaging and serving a flow, from your local development environment to a containerized application.
As you build your application, you will configure the following application behaviors. More detailed explanation is provided in the individual pages.

* [Custom Dependencies](/install-custom-dependencies) - Add and manage additional Python packages and external dependencies in your Langflow applications.

* [Memory and Storage](/memory) - Configure Langflow's storage and caching behavior.

* [Session Management](/session-id) - Use session ID to manage communication between components.

* [Logging](/logging) - Monitor and debug your Langflow applications.

* [Webhook](/webhook) - Trigger your flows with external requests.