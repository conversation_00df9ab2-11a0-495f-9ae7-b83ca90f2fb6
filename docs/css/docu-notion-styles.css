/* This should be added to the docusaurus.config.js in order to show some notion things correctly.
See the option: --css-output-directory
See the docusaurus docs: https://docusaurus.io/docs/styling-layout
See the use in the docu-notion-sample-site: https://github.com/sillsdev/docu-notion-sample-site/blob/main/docusaurus.config.js
*/

/* Copied from
    https://github1s.com/NotionX/react-notion-x/blob/master/packages/react-notion-x/src/styles.css#L934
    and
    https://github1s.com/NotionX/react-notion-x/blob/master/packages/react-notion-x/src/styles.css#L1063
*/
.notion-column {
  display: flex;
  flex-direction: column;
  padding-top: 12px;
  padding-bottom: 12px;
}

.notion-column > *:first-child {
  margin-top: 0;
  margin-left: 0;
  margin-right: 0;
}

.notion-column > *:last-child {
  margin-left: 0;
  margin-right: 0;
  margin-bottom: 0;
}

.notion-row {
  display: flex;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
}

@media (max-width: 640px) {
  .notion-row {
    flex-direction: column;
  }

  .notion-row .notion-column {
    width: 100% !important;
  }

  .notion-row .notion-spacer {
    display: none;
  }
}

.notion-spacer {
  /* This matches the value in ColumnTransformer.ts */
  width: calc(min(32px, 4vw));
}

.notion-spacer:last-child {
  display: none;
}
/* End copied from NotionX */
