/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */
:root {
  --ifm-background-color: var(--token-primary-bg-c);
  --ifm-color-primary: hsla(330, 81%, 60%, 1);
  --ifm-navbar-link-hover-color: initial;
  --ifm-navbar-padding-vertical: 0;
  --ifm-navbar-item-padding-vertical: 0;
  --ifm-font-family-base: Inter, -apple-system, BlinkMacSystemFont, Helvetica,
    Arial, sans-serif, "Apple Color Emoji", "Segoe UI emoji";
  --ifm-font-family-monospace: "SFMono-Regular", "Roboto Mono", Consolas,
    "Liberation Mono", Menlo, Courier, monospace;
}

.theme-doc-sidebar-item-category.menu__list-item:not(:first-child) {
  margin-top: 0.5rem !important;
}

.docusaurus-highlight-code-line {
  background-color: rgba(0, 0, 0, 0.1);
  display: block;
  margin: 0 calc(-1 * var(--ifm-pre-padding));
  padding: 0 var(--ifm-pre-padding);
}

.diagonal-box {
  transform: skewY(-6deg);
}

.diagonal-content {
  transform: skewY(6deg);
}

[class^="announcementBar"] {
  z-index: 10;
}

.showcase {
  background-color: #fff;
}

.showcase-border {
  border-color: rgba(243, 244, 246, 1);
}

.text-description {
  color: rgba(107, 114, 128, 1);
}

p {
  text-align: start;
}

/* apply */
#hero-apply {
  z-index: -1;
  background-image: linear-gradient(
    var(--ifm-footer-background-color),
    var(--ifm-navbar-background-color)
  );
}

.apply-form {
  background-image: linear-gradient(#fff, #f5f5fa);
  max-width: 600px;
}

.apply-text {
  color: #36395a;
}

/* index */
#hero {
  background-image: linear-gradient(
    var(--ifm-footer-background-color),
    var(--ifm-navbar-background-color)
  );
}

/**
  * Hero component title overrides to match other heading styles
  */
.hero-title {
  color: rgb(28, 30, 33);
  font-family: var(--ifm-heading-font-family);
}
h1 {
  font-size: 26px;
}
h2 {
  font-size: 22px;
}
h3 {
  font-size: 18px;
}

body {
  font-size: 16px;
}

.docsearch-logo {
  color: #21243d;
}

.apply-button:hover {
  color: #fff;
}

/* GitHub */
.header-github-link:hover {
  opacity: 0.6;
}

.header-github-link:before {
  content: "";
  width: 24px;
  height: 24px;
  display: flex;
  background: url("/logos/gitLight.svg") no-repeat;
}

[data-theme="dark"] .header-github-link:before {
  content: "";
  width: 24px;
  height: 24px;
  display: flex;
  background: url("/logos/gitDark.svg") no-repeat;
}

/* Twitter */
.header-twitter-link:hover {
  opacity: 0.6;
}

.header-twitter-link::before {
  content: "";
  width: 24px;
  height: 24px;
  display: flex;
  background: url("/logos/xLight.svg");
  background-size: contain;
}

[data-theme="dark"] .header-twitter-link::before {
  content: "";
  width: 24px;
  height: 24px;
  display: flex;
  background: url("/logos/xDark.svg");
  background-size: contain;
}

/* Discord */

.header-discord-link {
  margin-right: 0.5rem;
}

.header-discord-link:hover {
  opacity: 0.6;
}

[data-theme="dark"] .header-discord-link::before {
  content: "";
  width: 24px;
  height: 24px;
  display: flex;
  background: url("/logos/discordDark.svg");
  background-size: contain;
}

.header-discord-link::before {
  content: "";
  width: 24px;
  height: 24px;
  display: flex;
  background: url("/logos/discordLight.svg");
  background-size: contain;
}

/* Images */
.image-rendering-crisp {
  image-rendering: crisp-edges;

  /* alias for google chrome */
  image-rendering: -webkit-optimize-contrast;
}

.image-rendering-pixel {
  image-rendering: pixelated;
}

.img-center {
  display: flex;
  justify-content: center;
  width: 100%;
}

/* Add comprehensive image control */
.markdown img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1rem auto;
  max-height: 600px;
  object-fit: contain;
}

/* Default size for most images */
.markdown img:not(.resized-image) {
  max-width: 600px;
  width: auto;
}

.resized-image {
  width: 300px;
  max-height: 500px;
}

/* Responsive images on mobile devices */
@media (max-width: 768px) {
  .markdown img:not(.resized-image) {
    max-width: 100%;
    max-height: 500px;
  }

  .resized-image {
    width: 100%;
    max-width: 300px;
    max-height: 400px;
  }
}

/* Reduce width on mobile for Mendable Search */
@media (max-width: 767px) {
  .mendable-search {
    width: 200px;
  }
}

@media (max-width: 500px) {
  .mendable-search {
    width: 150px;
  }
}

@media (max-width: 380px) {
  .mendable-search {
    width: 140px;
  }
}
/*
.ch-scrollycoding {
  gap: 10rem !important;
} */

.ch-scrollycoding-content {
  max-width: 55% !important;
  min-width: 40% !important;
}

.ch-scrollycoding-sticker {
  max-width: 60% !important;
  min-width: 45% !important;
}

.ch-scrollycoding-step-content {
  min-height: 70px;
}

.theme-doc-sidebar-item-category.theme-doc-sidebar-item-category-level-2.menu__list-item:not(
    :first-child
  ) {
  margin-top: 0.15rem !important;
}

/* Footer Styles */
.footer {
  padding: 8px 0;
  background-color: var(--ifm-navbar-background-color);
}

[data-theme="light"] .footer {
  border-top: 1px solid var(--ifm-color-emphasis-300);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 var(--ifm-navbar-padding-horizontal);
  max-width: var(--ifm-container-width);
  margin: 0 auto;
  font-size: 12px;
}

.footer__copyright {
  color: var(--ifm-toc-link-color);
}

.footer-links {
  display: flex;
  gap: 0.5rem;
  font-size: 13px;
}

.footer-links a {
  color: var(--ifm-toc-link-color);
  text-decoration: none;
}

.footer-links a:hover {
  text-decoration: underline;
}

.footer .container {
  padding: 0 5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row-reverse;
}

/* Sidebar Styles */
.theme-doc-sidebar-container nav.menu {
  padding-bottom: 0;
}

.theme-doc-sidebar-menu {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.theme-doc-sidebar-menu > :nth-last-child(2) {
  margin-bottom: auto;
}

/* Sidebar Ad Styles */
.sidebar-ad {
  margin-left: -0.5rem;
  margin-right: -0.5rem;
  margin-top: 1rem;
  border-radius: 0;
  border-top: 1px solid var(--ifm-color-emphasis-300);
  position: sticky;
  bottom: 0;
}

[data-theme="dark"] .sidebar-ad {
  background-color: var(--ifm-background-color);
}

[data-theme="light"] .sidebar-ad {
  background-color: var(--ifm-color-white);
}

.sidebar-ad a {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.sidebar-ad-text-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.sidebar-ad-text {
  font-size: 0.875rem;
  font-family: "Sora", serif;
}

.sidebar-ad-text-gradient {
  color: transparent;
  background-clip: text;
  background-image: linear-gradient(
    to right,
    #a855f7,
    var(--ifm-color-primary)
  );
}

/* Component details styling */
.markdown details {
  width: 100%;
  background: var(--ifm-background-color);
  border: 1px solid var(--ifm-color-emphasis-300);
  border-radius: var(--ifm-global-radius);
  margin-bottom: 1rem;
}

.markdown details summary {
  display: flex;
  align-items: center;
  gap: 0.5em;
  list-style: none;
  outline: none;
  cursor: pointer;
}

.markdown details summary:focus-visible {
  outline: 2px solid var(--ifm-color-primary, #a855f7);
  outline-offset: 2px;
}

.markdown details [class^="collapsibleContent"] {
  border-top: none !important;
  padding: 0;
}

