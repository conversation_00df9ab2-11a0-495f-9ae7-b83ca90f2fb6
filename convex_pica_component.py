"""
Convex Component using Pica OS Tools for Langflow
This component provides integration with Convex databases through Pica OS MCP tools.
"""

import json
from typing import Any, Dict, List, Optional

import httpx
from loguru import logger

from langflow.custom.custom_component.component import Component
from langflow.io import (
    BoolInput,
    DropdownInput,
    MessageTextInput,
    MultilineInput,
    Output,
    SecretStrInput,
    StrInput,
)
from langflow.schema.data import Data
from langflow.schema.message import Message


class ConvexPicaComponent(Component):
    """
    A Langflow component that integrates with Convex databases using Pica OS tools.
    Supports queries, mutations, and function execution.
    """

    display_name = "Convex (Pica)"
    description = "Execute Convex functions, queries, and mutations using Pica OS integration"
    documentation = "https://docs.convex.dev/api"
    icon = "database"
    name = "ConvexPica"

    inputs = [
        # Pica Configuration
        SecretStrInput(
            name="pica_api_key",
            display_name="Pica API Key",
            info="Your Pica OS API key for authentication",
            required=True,
        ),
        StrInput(
            name="pica_connection_key",
            display_name="Pica Connection Key",
            info="Your Pica Convex connection key (format: live::convex::default::xxxxx)",
            required=True,
        ),
        
        # Convex Configuration
        DropdownInput(
            name="operation_type",
            display_name="Operation Type",
            options=["query", "mutation", "action", "run_function"],
            value="query",
            info="Type of Convex operation to perform",
        ),
        MessageTextInput(
            name="function_path",
            display_name="Function Path",
            info="Path to the Convex function (e.g., 'messages/list' or 'users/create')",
            placeholder="messages/list",
            required=True,
        ),
        MultilineInput(
            name="function_args",
            display_name="Function Arguments",
            info="JSON object with arguments to pass to the function",
            placeholder='{"limit": 10, "filter": "active"}',
            value="{}",
        ),
        BoolInput(
            name="include_logs",
            display_name="Include Logs",
            info="Include execution logs in the response",
            value=True,
        ),
    ]

    outputs = [
        Output(display_name="Result", name="result", method="execute_convex_operation"),
        Output(display_name="Raw Response", name="raw_response", method="get_raw_response"),
    ]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._last_response = None

    async def _make_pica_request(self, action_id: str, method: str, path: str, data: Optional[Dict] = None) -> Dict:
        """Make a request to Pica OS API to execute Convex operations."""

        # Use the actual Pica execute endpoint
        pica_url = "https://api.pica.ai/v1/execute"

        headers = {
            "Authorization": f"Bearer {self.pica_api_key}",
            "Content-Type": "application/json",
        }

        payload = {
            "actionId": action_id,
            "connectionKey": self.pica_connection_key,
            "method": method,
            "path": path,
        }

        if data:
            payload["data"] = data

        logger.info(f"Making Pica request: {self.operation_type} to {path}")

        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(pica_url, headers=headers, json=payload, timeout=60.0)
                response.raise_for_status()
                result = response.json()

                # Log success
                logger.info(f"Pica request successful: {result.get('status', 'unknown')}")
                return result

            except httpx.TimeoutException:
                logger.error("Pica API request timed out")
                raise ValueError("Request timed out. Convex operation may be taking too long.")
            except httpx.HTTPStatusError as e:
                logger.error(f"Pica API HTTP error: {e.response.status_code} - {e.response.text}")
                raise ValueError(f"Pica API error ({e.response.status_code}): {e.response.text}")
            except httpx.HTTPError as e:
                logger.error(f"Pica API request failed: {e}")
                raise ValueError(f"Failed to execute Convex operation: {e}")
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON response from Pica API: {e}")
                raise ValueError("Invalid response format from Pica API")

    def _parse_function_args(self) -> Dict[str, Any]:
        """Parse the function arguments from JSON string."""
        try:
            if not self.function_args or self.function_args.strip() == "":
                return {}
            return json.loads(self.function_args)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in function_args: {e}")
            raise ValueError(f"Invalid JSON in function arguments: {e}")

    def _get_action_id_for_operation(self) -> str:
        """Get the appropriate Pica action ID based on operation type."""
        action_mapping = {
            "query": "conn_mod_def::GC-4oMx93W0::b73lKTyLQjOaCct8g4XBpg",  # Function Query
            "mutation": "conn_mod_def::GC-4pKAk_EA::0Dl8rEahS3KB7V31-rULgw",  # Function Mutation
            "action": "conn_mod_def::GC-4ptj4lUY::xTsp3aO3SzuViXlg-K5twQ",  # Function Action
            "run_function": "conn_mod_def::GC-4tAr2fbg::-QIp8AdiRHKLNlNG3bpt5g",  # Run Convex Function
        }
        return action_mapping.get(self.operation_type, action_mapping["query"])

    def _get_api_path_for_operation(self) -> str:
        """Get the appropriate API path based on operation type."""
        path_mapping = {
            "query": "/api/query",
            "mutation": "/api/mutation", 
            "action": "/api/action",
            "run_function": f"/api/run/{self.function_path.replace(':', '/')}",
        }
        return path_mapping.get(self.operation_type, path_mapping["query"])

    async def execute_convex_operation(self) -> Data:
        """Execute the Convex operation and return structured data."""
        try:
            # Parse function arguments
            args = self._parse_function_args()
            
            # Get action ID and path
            action_id = self._get_action_id_for_operation()
            api_path = self._get_api_path_for_operation()
            
            # Prepare request data
            if self.operation_type == "run_function":
                request_data = {
                    "args": args,
                    "format": "json"
                }
            else:
                request_data = {
                    "path": self.function_path,
                    "args": args,
                    "format": "json"
                }
            
            # Make the request through Pica
            response = await self._make_pica_request(
                action_id=action_id,
                method="POST",
                path=api_path,
                data=request_data
            )
            
            # Store response for raw output
            self._last_response = response
            
            # Process the response
            if response.get("status") == "success":
                result_data = {
                    "success": True,
                    "value": response.get("value"),
                    "operation": self.operation_type,
                    "function_path": self.function_path,
                }
                
                if self.include_logs and "logLines" in response:
                    result_data["logs"] = response["logLines"]
                    
                return Data(data=result_data, value=response.get("value"))
            else:
                # Handle error response
                error_data = {
                    "success": False,
                    "error": response.get("errorMessage", "Unknown error"),
                    "operation": self.operation_type,
                    "function_path": self.function_path,
                }
                
                if "errorData" in response:
                    error_data["error_details"] = response["errorData"]
                    
                if self.include_logs and "logLines" in response:
                    error_data["logs"] = response["logLines"]
                    
                return Data(data=error_data, value=None)
                
        except Exception as e:
            logger.error(f"Error executing Convex operation: {e}")
            error_data = {
                "success": False,
                "error": str(e),
                "operation": self.operation_type,
                "function_path": self.function_path,
            }
            return Data(data=error_data, value=None)

    def get_raw_response(self) -> Message:
        """Return the raw response from the last operation."""
        if self._last_response is None:
            return Message(text="No operation has been executed yet.")

        return Message(text=json.dumps(self._last_response, indent=2))

    # Additional utility methods for common Convex operations

    def validate_inputs(self) -> List[str]:
        """Validate component inputs and return list of errors."""
        errors = []

        if not self.pica_api_key:
            errors.append("Pica API key is required")

        if not self.pica_connection_key:
            errors.append("Pica connection key is required")
        elif not self.pica_connection_key.startswith("live::convex::"):
            errors.append("Invalid Pica connection key format. Should start with 'live::convex::'")

        if not self.function_path:
            errors.append("Function path is required")
        elif ":" in self.function_path and self.operation_type != "run_function":
            errors.append("Function path should use '/' separator, not ':'")

        try:
            self._parse_function_args()
        except ValueError as e:
            errors.append(f"Invalid function arguments: {e}")

        return errors

    @staticmethod
    def get_example_usage() -> Dict[str, Any]:
        """Return example usage for documentation."""
        return {
            "query_example": {
                "operation_type": "query",
                "function_path": "messages/list",
                "function_args": '{"limit": 10, "channel": "general"}'
            },
            "mutation_example": {
                "operation_type": "mutation",
                "function_path": "messages/create",
                "function_args": '{"text": "Hello World", "author": "user123"}'
            },
            "action_example": {
                "operation_type": "action",
                "function_path": "notifications/send",
                "function_args": '{"userId": "user123", "message": "Welcome!"}'
            }
        }
