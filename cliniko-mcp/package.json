{"name": "cliniko-mcp", "version": "1.0.0", "description": "Model Context Protocol (MCP) server for Cliniko API - enables AI agents and Langflow to interact with Cliniko practice management system", "main": "dist/index.js", "bin": {"cliniko-mcp": "./dist/mcp/index.js"}, "scripts": {"build": "tsc", "start": "node dist/mcp/index.js", "start:http": "CLINIKO_MCP_MODE=http node dist/mcp/index.js", "dev": "npm run build && npm run start", "dev:http": "CLINIKO_MCP_MODE=http nodemon --watch src --ext ts --exec 'npm run build && npm run start:http'", "test": "jest", "test:watch": "jest --watch", "lint": "tsc --noEmit", "typecheck": "tsc --noEmit", "validate": "node dist/scripts/validate.js", "docs": "typedoc src --out docs/api"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/cliniko-mcp.git"}, "keywords": ["cliniko", "mcp", "model-context-protocol", "healthcare", "practice-management", "ai", "langflow", "medical", "appointments", "patients"], "author": "Your Name", "license": "MIT", "bugs": {"url": "https://github.com/your-org/cliniko-mcp/issues"}, "homepage": "https://github.com/your-org/cliniko-mcp#readme", "files": ["dist/**/*", ".env.example", "README.md", "LICENSE"], "devDependencies": {"@types/node": "^22.15.30", "@types/jest": "^29.5.14", "@types/express": "^5.0.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typedoc": "^0.26.11"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.13.2", "axios": "^1.10.0", "dotenv": "^16.5.0", "express": "^4.21.2", "uuid": "^10.0.0", "zod": "^3.24.1"}, "engines": {"node": ">=16.0.0"}, "optionalDependencies": {}}