# Cliniko MCP Server Configuration

# MCP Server Mode
CLINIKO_MCP_MODE=stdio  # stdio (for Claude Desktop/Langflow) or http (for remote access)
LOG_LEVEL=error         # error, warn, info, debug
DISABLE_CONSOLE_OUTPUT=true  # Disable console output in stdio mode

# Cliniko API Configuration (Required)
CLINIKO_API_KEY=your-cliniko-api-key-here  # Your Cliniko API key
CLINIKO_SHARD=au1                          # Your Cliniko shard (au1, au2, us1, etc.)

# Optional: Auto-detect shard from API key (if not specified above)
CLINIKO_AUTO_DETECT_SHARD=true

# Optional: Rate Limiting
CLINIKO_RATE_LIMIT=200          # Requests per minute (<PERSON>liniko default: 200)
CLINIKO_RATE_LIMIT_WINDOW=60000 # Rate limit window in milliseconds

# Optional: Request Configuration
CLINIKO_REQUEST_TIMEOUT=30000   # Request timeout in milliseconds
CLINIKO_RETRY_ATTEMPTS=3        # Number of retry attempts for failed requests
CLINIKO_RETRY_DELAY=1000        # Delay between retries in milliseconds

# Optional: User Agent Configuration
CLINIKO_USER_AGENT=Cliniko MCP Server (<EMAIL>)

# Optional: HTTP Server Settings (only for http mode)
HTTP_PORT=3000
HTTP_HOST=localhost

# Optional: Logging
CLINIKO_MCP_LOG_PATH=./logs/cliniko-mcp.log  # Path to log file

# Optional: Cache Settings
CACHE_TTL=300000  # Cache TTL in milliseconds (5 minutes)
ENABLE_CACHE=true # Enable response caching

# Optional: Validation Settings
STRICT_VALIDATION=true  # Enable strict input validation
VALIDATE_RESPONSES=true # Validate API responses

# Optional: Development Settings
DEBUG_API_CALLS=false   # Log all API calls for debugging
MOCK_API_RESPONSES=false # Use mock responses for testing
