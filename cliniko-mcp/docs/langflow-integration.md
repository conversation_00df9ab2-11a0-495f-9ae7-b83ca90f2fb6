# Langflow Integration Guide

This guide shows how to integrate Cliniko MCP with Langflow to create powerful healthcare workflow automations.

## 🔧 Setup

### 1. Install Cliniko MCP

```bash
# Option 1: Use npx (recommended)
npx cliniko-mcp

# Option 2: Local installation
npm install -g cliniko-mcp
```

### 2. Configure Langflow MCP

Add to your Langflow MCP configuration file:

```json
{
  "mcpServers": {
    "cliniko-mcp": {
      "command": "npx",
      "args": ["cliniko-mcp"],
      "env": {
        "CLINIKO_MCP_MODE": "stdio",
        "CLINIKO_API_KEY": "your-cliniko-api-key-here",
        "CLINIKO_SHARD": "au1",
        "LOG_LEVEL": "error",
        "DISABLE_CONSOLE_OUTPUT": "true"
      }
    }
  }
}
```

### 3. Get Your Cliniko API Key

1. Log into your Cliniko account
2. Go to **Settings** → **API Keys**
3. Click **Generate new API key**
4. Copy the key and add it to your configuration

## 🏗️ Creating Langflow Components

### Patient Management Component

Create a custom Langflow component for patient operations:

```python
from langflow import CustomComponent
from typing import Optional, Dict, Any
import json

class ClinikoPatientComponent(CustomComponent):
    display_name = "Cliniko Patient Manager"
    description = "Manage Cliniko patients - search, create, update"
    
    def build_config(self):
        return {
            "operation": {
                "display_name": "Operation",
                "options": ["search", "create", "update", "get"],
                "value": "search"
            },
            "patient_data": {
                "display_name": "Patient Data",
                "multiline": True,
                "info": "JSON object with patient information"
            },
            "search_query": {
                "display_name": "Search Query",
                "info": "Search term for finding patients"
            },
            "patient_id": {
                "display_name": "Patient ID",
                "info": "Patient ID for get/update operations"
            }
        }
    
    def build(
        self,
        operation: str,
        patient_data: Optional[str] = None,
        search_query: Optional[str] = None,
        patient_id: Optional[str] = None
    ) -> Dict[str, Any]:
        
        # Use MCP tools based on operation
        if operation == "search":
            return self.search_patients(search_query)
        elif operation == "create":
            return self.create_patient(patient_data)
        elif operation == "update":
            return self.update_patient(patient_id, patient_data)
        elif operation == "get":
            return self.get_patient(patient_id)
    
    def search_patients(self, query: str) -> Dict[str, Any]:
        # This would call the MCP tool
        # In practice, Langflow will handle the MCP integration
        return {
            "operation": "search_patients",
            "query": query,
            "mcp_tool": "cliniko_search_patients",
            "parameters": {"query": query, "limit": 10}
        }
    
    def create_patient(self, patient_data: str) -> Dict[str, Any]:
        try:
            data = json.loads(patient_data) if patient_data else {}
            return {
                "operation": "create_patient",
                "mcp_tool": "cliniko_create_patient",
                "parameters": data
            }
        except json.JSONDecodeError:
            return {"error": "Invalid JSON in patient_data"}
    
    def update_patient(self, patient_id: str, patient_data: str) -> Dict[str, Any]:
        try:
            data = json.loads(patient_data) if patient_data else {}
            data["patient_id"] = int(patient_id)
            return {
                "operation": "update_patient",
                "mcp_tool": "cliniko_update_patient",
                "parameters": data
            }
        except (json.JSONDecodeError, ValueError):
            return {"error": "Invalid patient_id or patient_data"}
    
    def get_patient(self, patient_id: str) -> Dict[str, Any]:
        try:
            return {
                "operation": "get_patient",
                "mcp_tool": "cliniko_get_patient",
                "parameters": {"patient_id": int(patient_id)}
            }
        except ValueError:
            return {"error": "Invalid patient_id"}
```

### Appointment Scheduling Component

```python
class ClinikoAppointmentComponent(CustomComponent):
    display_name = "Cliniko Appointment Scheduler"
    description = "Schedule and manage Cliniko appointments"
    
    def build_config(self):
        return {
            "operation": {
                "display_name": "Operation",
                "options": ["create", "list", "update", "cancel", "check_availability"],
                "value": "create"
            },
            "appointment_data": {
                "display_name": "Appointment Data",
                "multiline": True,
                "info": "JSON object with appointment details"
            },
            "patient_id": {
                "display_name": "Patient ID",
                "info": "ID of the patient for the appointment"
            },
            "practitioner_id": {
                "display_name": "Practitioner ID",
                "info": "ID of the practitioner"
            },
            "appointment_start": {
                "display_name": "Start Time",
                "info": "Appointment start time (YYYY-MM-DDTHH:mm:ssZ)"
            },
            "appointment_end": {
                "display_name": "End Time",
                "info": "Appointment end time (YYYY-MM-DDTHH:mm:ssZ)"
            }
        }
    
    def build(
        self,
        operation: str,
        appointment_data: Optional[str] = None,
        patient_id: Optional[str] = None,
        practitioner_id: Optional[str] = None,
        appointment_start: Optional[str] = None,
        appointment_end: Optional[str] = None
    ) -> Dict[str, Any]:
        
        if operation == "create":
            return self.create_appointment(
                appointment_data, patient_id, practitioner_id,
                appointment_start, appointment_end
            )
        elif operation == "list":
            return self.list_appointments(patient_id, practitioner_id)
        elif operation == "check_availability":
            return self.check_availability(
                practitioner_id, appointment_start, appointment_end
            )
    
    def create_appointment(
        self, appointment_data: str, patient_id: str,
        practitioner_id: str, start_time: str, end_time: str
    ) -> Dict[str, Any]:
        try:
            data = json.loads(appointment_data) if appointment_data else {}
            
            # Override with individual parameters if provided
            if patient_id:
                data["patient_id"] = int(patient_id)
            if practitioner_id:
                data["practitioner_id"] = int(practitioner_id)
            if start_time:
                data["appointment_start"] = start_time
            if end_time:
                data["appointment_end"] = end_time
            
            return {
                "operation": "create_appointment",
                "mcp_tool": "cliniko_create_individual_appointment",
                "parameters": data
            }
        except (json.JSONDecodeError, ValueError) as e:
            return {"error": f"Invalid data: {str(e)}"}
    
    def list_appointments(
        self, patient_id: str, practitioner_id: str
    ) -> Dict[str, Any]:
        params = {}
        if patient_id:
            params["patient_id"] = int(patient_id)
        if practitioner_id:
            params["practitioner_id"] = int(practitioner_id)
        
        return {
            "operation": "list_appointments",
            "mcp_tool": "cliniko_list_individual_appointments",
            "parameters": params
        }
    
    def check_availability(
        self, practitioner_id: str, start_time: str, end_time: str
    ) -> Dict[str, Any]:
        return {
            "operation": "check_availability",
            "mcp_tool": "cliniko_check_appointment_conflicts",
            "parameters": {
                "practitioner_id": int(practitioner_id),
                "appointment_start": start_time,
                "appointment_end": end_time,
                "business_id": 1  # You might want to make this configurable
            }
        }
```

## 🔄 Example Workflows

### 1. Patient Registration Workflow

```
[Text Input: Patient Info] 
    ↓
[JSON Parser: Parse patient data]
    ↓
[Cliniko Patient Component: Create patient]
    ↓
[Conditional: Check if successful]
    ↓
[Email Component: Send welcome email]
```

### 2. Appointment Booking Workflow

```
[Text Input: Appointment request]
    ↓
[LLM: Extract appointment details]
    ↓
[Cliniko Patient Component: Search for patient]
    ↓
[Cliniko Appointment Component: Check availability]
    ↓
[Conditional: If available]
    ↓
[Cliniko Appointment Component: Create appointment]
    ↓
[SMS Component: Send confirmation]
```

### 3. Patient Follow-up Workflow

```
[Schedule Trigger: Daily at 9 AM]
    ↓
[Cliniko Appointment Component: List today's appointments]
    ↓
[Loop: For each appointment]
    ↓
[Cliniko Patient Component: Get patient details]
    ↓
[LLM: Generate personalized follow-up message]
    ↓
[Email/SMS Component: Send follow-up]
```

### 4. Medical Alert Monitoring

```
[Webhook: New patient created]
    ↓
[Cliniko Patient Component: Get patient details]
    ↓
[LLM: Analyze medical history for alerts]
    ↓
[Conditional: If alerts needed]
    ↓
[Cliniko Medical Alert Component: Create alert]
    ↓
[Notification: Alert staff]
```

## 🎯 Best Practices

### 1. Error Handling

Always include error handling in your workflows:

```python
def handle_cliniko_response(response: Dict[str, Any]) -> Dict[str, Any]:
    if "error" in response:
        return {
            "success": False,
            "error": response["error"],
            "action": "log_error_and_notify_admin"
        }
    return {
        "success": True,
        "data": response,
        "action": "continue_workflow"
    }
```

### 2. Data Validation

Validate data before sending to Cliniko:

```python
def validate_patient_data(data: Dict[str, Any]) -> bool:
    required_fields = ["first_name", "last_name"]
    return all(field in data and data[field] for field in required_fields)
```

### 3. Rate Limiting

Be mindful of Cliniko's rate limits (200 requests/minute):

```python
import time

def rate_limited_cliniko_call(mcp_tool: str, params: Dict[str, Any]):
    # Add delay between calls if needed
    time.sleep(0.3)  # 300ms delay = max 200 requests/minute
    return call_mcp_tool(mcp_tool, params)
```

### 4. Logging and Monitoring

Log all Cliniko interactions for debugging:

```python
import logging

def log_cliniko_operation(operation: str, params: Dict[str, Any], result: Dict[str, Any]):
    logging.info(f"Cliniko {operation}: {params} -> {result}")
```

## 🔍 Debugging

### Common Issues

1. **API Key Issues**
   - Verify API key is correct
   - Check shard configuration
   - Ensure API key has necessary permissions

2. **Data Format Errors**
   - Validate JSON structure
   - Check date/time formats (ISO 8601)
   - Verify required fields are present

3. **Rate Limiting**
   - Add delays between requests
   - Implement retry logic
   - Monitor rate limit headers

### Debug Mode

Enable debug logging:

```bash
LOG_LEVEL=debug CLINIKO_MCP_MODE=stdio npx cliniko-mcp
```

## 📚 Additional Resources

- [Cliniko API Documentation](https://docs.api.cliniko.com/)
- [Langflow Documentation](https://docs.langflow.org/)
- [MCP Protocol Specification](https://modelcontextprotocol.io/)
- [Example Workflows Repository](./examples/)

## 🆘 Support

For issues with the Cliniko MCP integration:

1. Check the [troubleshooting guide](./troubleshooting.md)
2. Review the [API documentation](./api-reference.md)
3. Open an issue on GitHub
4. Contact support with detailed error logs
