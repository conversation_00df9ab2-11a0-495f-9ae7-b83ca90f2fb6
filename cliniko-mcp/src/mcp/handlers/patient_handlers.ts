import { ClinikoApiClient } from '../../services/cliniko_api';
import { Patient, PatientCreate, PatientUpdate, MedicalAlert } from '../../models/patient';
import { ClinikoListResponse, ClinikoFilterOptions, buildFilterQuery } from '../../models/common';
import { logger } from '../../utils/logger';

export class PatientHandlers {
  constructor(private apiClient: ClinikoApiClient) {}

  async handleTool(toolName: string, args: any): Promise<any> {
    switch (toolName) {
      case 'cliniko_list_patients':
        return this.handleListPatients(args);
      case 'cliniko_get_patient':
        return this.handleGetPatient(args);
      case 'cliniko_search_patients':
        return this.handleSearchPatients(args);
      case 'cliniko_create_patient':
        return this.handleCreatePatient(args);
      case 'cliniko_update_patient':
        return this.handleUpdatePatient(args);
      case 'cliniko_archive_patient':
        return this.handleArchivePatient(args);
      case 'cliniko_unarchive_patient':
        return this.handleUnarchivePatient(args);
      case 'cliniko_get_patient_medical_alerts':
        return this.handleGetPatientMedicalAlerts(args);
      case 'cliniko_create_medical_alert':
        return this.handleCreateMedicalAlert(args);
      case 'cliniko_get_patient_attachments':
        return this.handleGetPatientAttachments(args);
      default:
        throw new Error(`Unknown patient management tool: ${toolName}`);
    }
  }

  private async handleListPatients(args: {
    page?: number;
    per_page?: number;
    first_name?: string;
    last_name?: string;
    email?: string;
    phone_number?: string;
    mobile_number?: string;
    date_of_birth?: string;
    created_after?: string;
    updated_after?: string;
    include_archived?: boolean;
    sort?: string;
    order?: 'asc' | 'desc';
  }): Promise<any> {
    try {
      const filters: string[] = [];

      // Build filter conditions
      if (args.first_name) {
        filters.push(`first_name:~${args.first_name}`);
      }
      if (args.last_name) {
        filters.push(`last_name:~${args.last_name}`);
      }
      if (args.email) {
        filters.push(`email:=${args.email}`);
      }
      if (args.phone_number) {
        filters.push(`phone_number:~${args.phone_number}`);
      }
      if (args.mobile_number) {
        filters.push(`mobile_number:~${args.mobile_number}`);
      }
      if (args.date_of_birth) {
        filters.push(`date_of_birth:=${args.date_of_birth}`);
      }
      if (args.created_after) {
        filters.push(`created_at:>=${args.created_after}`);
      }
      if (args.updated_after) {
        filters.push(`updated_at:>=${args.updated_after}`);
      }
      if (args.include_archived) {
        filters.push('archived_at:*');
      }

      const options: ClinikoFilterOptions = {
        page: args.page || 1,
        per_page: Math.min(args.per_page || 50, 100),
        q: filters,
        sort: args.sort || 'created_at',
        order: args.order || 'desc',
      };

      const response = await this.apiClient.list<Patient>('/patients', options);

      return {
        patients: response.patients || [],
        total_entries: response.total_entries,
        pagination: {
          current_page: args.page || 1,
          per_page: args.per_page || 50,
          total_entries: response.total_entries,
          has_more: !!response.links.next,
        },
        filters_applied: {
          first_name: args.first_name,
          last_name: args.last_name,
          email: args.email,
          include_archived: args.include_archived || false,
        },
      };
    } catch (error) {
      logger.error('Error listing patients:', error);
      throw new Error(`Failed to list patients: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleGetPatient(args: { patient_id: number }): Promise<any> {
    try {
      const patient = await this.apiClient.get<{ patient: Patient }>(`/patients/${args.patient_id}`);

      return {
        patient: patient.patient,
        summary: {
          full_name: `${patient.patient.first_name} ${patient.patient.last_name}`,
          preferred_name: patient.patient.preferred_first_name || patient.patient.first_name,
          contact_info: {
            email: patient.patient.email,
            phone: patient.patient.phone_number,
            mobile: patient.patient.mobile_number,
          },
          address: {
            street: [patient.patient.address_1, patient.patient.address_2, patient.patient.address_3]
              .filter(Boolean).join(', '),
            city: patient.patient.city,
            state: patient.patient.state,
            post_code: patient.patient.post_code,
            country: patient.patient.country,
          },
          demographics: {
            date_of_birth: patient.patient.date_of_birth,
            gender: patient.patient.gender,
            occupation: patient.patient.occupation,
          },
          medical_info: {
            medicare_number: patient.patient.medicare_reference_number,
            healthcare_identifier: patient.patient.healthcare_identifier,
            dva_card: patient.patient.dva_card_number,
          },
        },
      };
    } catch (error) {
      logger.error('Error getting patient:', error);
      throw new Error(`Failed to get patient: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleSearchPatients(args: {
    query?: string;
    first_name?: string;
    last_name?: string;
    email?: string;
    phone?: string;
    date_of_birth?: string;
    medicare_number?: string;
    healthcare_identifier?: string;
    limit?: number;
  }): Promise<any> {
    try {
      const filters: string[] = [];

      // General query search across multiple fields
      if (args.query) {
        // Search in name and email
        filters.push(`first_name:~${args.query}`);
        filters.push(`last_name:~${args.query}`);
        filters.push(`email:~${args.query}`);
      }

      // Specific field searches
      if (args.first_name) {
        filters.push(`first_name:~${args.first_name}`);
      }
      if (args.last_name) {
        filters.push(`last_name:~${args.last_name}`);
      }
      if (args.email) {
        filters.push(`email:~${args.email}`);
      }
      if (args.phone) {
        filters.push(`phone_number:~${args.phone}`);
        filters.push(`mobile_number:~${args.phone}`);
      }
      if (args.date_of_birth) {
        filters.push(`date_of_birth:=${args.date_of_birth}`);
      }
      if (args.medicare_number) {
        filters.push(`medicare_reference_number:~${args.medicare_number}`);
      }
      if (args.healthcare_identifier) {
        filters.push(`healthcare_identifier:~${args.healthcare_identifier}`);
      }

      const options: ClinikoFilterOptions = {
        per_page: Math.min(args.limit || 20, 100),
        q: filters,
        sort: 'last_name',
        order: 'asc',
      };

      const response = await this.apiClient.list<Patient>('/patients', options);

      return {
        query: args.query || 'advanced search',
        results: (response.patients || []).map(patient => ({
          id: patient.id,
          full_name: `${patient.first_name} ${patient.last_name}`,
          preferred_name: patient.preferred_first_name || patient.first_name,
          email: patient.email,
          phone: patient.phone_number,
          mobile: patient.mobile_number,
          date_of_birth: patient.date_of_birth,
          address: {
            city: patient.city,
            state: patient.state,
            post_code: patient.post_code,
          },
          created_at: patient.created_at,
          updated_at: patient.updated_at,
        })),
        total_found: response.total_entries,
        showing: (response.patients || []).length,
        search_criteria: {
          general_query: args.query,
          first_name: args.first_name,
          last_name: args.last_name,
          email: args.email,
          phone: args.phone,
          date_of_birth: args.date_of_birth,
        },
      };
    } catch (error) {
      logger.error('Error searching patients:', error);
      throw new Error(`Failed to search patients: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleCreatePatient(args: PatientCreate): Promise<any> {
    try {
      // Validate required fields
      if (!args.first_name || !args.last_name) {
        throw new Error('First name and last name are required');
      }

      // Validate email format if provided
      if (args.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(args.email)) {
        throw new Error('Invalid email format');
      }

      // Validate date of birth format if provided
      if (args.date_of_birth && !/^\d{4}-\d{2}-\d{2}$/.test(args.date_of_birth)) {
        throw new Error('Date of birth must be in YYYY-MM-DD format');
      }

      const patientData = {
        patient: args,
      };

      const response = await this.apiClient.post<{ patient: Patient }>('/patients', patientData);

      return {
        success: true,
        patient: response.patient,
        message: `Patient "${response.patient.first_name} ${response.patient.last_name}" created successfully`,
        patient_id: response.patient.id,
        summary: {
          full_name: `${response.patient.first_name} ${response.patient.last_name}`,
          email: response.patient.email,
          phone: response.patient.phone_number,
          mobile: response.patient.mobile_number,
          date_of_birth: response.patient.date_of_birth,
        },
      };
    } catch (error) {
      logger.error('Error creating patient:', error);
      throw new Error(`Failed to create patient: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleUpdatePatient(args: PatientUpdate & { patient_id: number }): Promise<any> {
    try {
      const { patient_id, ...updateData } = args;

      // Validate email format if provided
      if (updateData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(updateData.email)) {
        throw new Error('Invalid email format');
      }

      // Validate date of birth format if provided
      if (updateData.date_of_birth && !/^\d{4}-\d{2}-\d{2}$/.test(updateData.date_of_birth)) {
        throw new Error('Date of birth must be in YYYY-MM-DD format');
      }

      const patientData = {
        patient: updateData,
      };

      const response = await this.apiClient.patch<{ patient: Patient }>(`/patients/${patient_id}`, patientData);

      return {
        success: true,
        patient: response.patient,
        message: `Patient "${response.patient.first_name} ${response.patient.last_name}" updated successfully`,
        changes_applied: Object.keys(updateData),
      };
    } catch (error) {
      logger.error('Error updating patient:', error);
      throw new Error(`Failed to update patient: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleArchivePatient(args: { patient_id: number }): Promise<any> {
    try {
      await this.apiClient.archive(`/patients/${args.patient_id}`);

      return {
        success: true,
        patient_id: args.patient_id,
        message: 'Patient archived successfully',
        note: 'Archived patients are hidden from normal views but can be unarchived if needed',
      };
    } catch (error) {
      logger.error('Error archiving patient:', error);
      throw new Error(`Failed to archive patient: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleUnarchivePatient(args: { patient_id: number }): Promise<any> {
    try {
      await this.apiClient.unarchive(`/patients/${args.patient_id}`);

      return {
        success: true,
        patient_id: args.patient_id,
        message: 'Patient unarchived successfully',
        note: 'Patient is now visible in normal views again',
      };
    } catch (error) {
      logger.error('Error unarchiving patient:', error);
      throw new Error(`Failed to unarchive patient: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleGetPatientMedicalAlerts(args: { patient_id: number }): Promise<any> {
    try {
      const response = await this.apiClient.list<MedicalAlert>('/medical_alerts', {
        q: [`patient_id:=${args.patient_id}`],
      });

      return {
        patient_id: args.patient_id,
        medical_alerts: response.medical_alerts || [],
        total_alerts: response.total_entries,
        summary: {
          alert_count: response.total_entries,
          alert_types: [...new Set((response.medical_alerts || []).map(alert => alert.alert_type).filter(Boolean))],
        },
      };
    } catch (error) {
      logger.error('Error getting patient medical alerts:', error);
      throw new Error(`Failed to get medical alerts: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleCreateMedicalAlert(args: {
    patient_id: number;
    name: string;
    description?: string;
    alert_type?: string;
  }): Promise<any> {
    try {
      const alertData = {
        medical_alert: {
          name: args.name,
          description: args.description,
          alert_type: args.alert_type,
          patient_id: args.patient_id,
        },
      };

      const response = await this.apiClient.post<{ medical_alert: MedicalAlert }>('/medical_alerts', alertData);

      return {
        success: true,
        medical_alert: response.medical_alert,
        message: `Medical alert "${response.medical_alert.name}" created successfully`,
        patient_id: args.patient_id,
      };
    } catch (error) {
      logger.error('Error creating medical alert:', error);
      throw new Error(`Failed to create medical alert: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleGetPatientAttachments(args: { patient_id: number }): Promise<any> {
    try {
      const response = await this.apiClient.list('/patient_attachments', {
        q: [`patient_id:=${args.patient_id}`],
      });

      return {
        patient_id: args.patient_id,
        attachments: response.patient_attachments || [],
        total_attachments: response.total_entries,
        summary: {
          attachment_count: response.total_entries,
          file_types: [...new Set((response.patient_attachments || []).map((att: any) => att.file_type))],
          total_size_bytes: (response.patient_attachments || []).reduce((sum: number, att: any) => sum + (att.file_size || 0), 0),
        },
      };
    } catch (error) {
      logger.error('Error getting patient attachments:', error);
      throw new Error(`Failed to get patient attachments: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
