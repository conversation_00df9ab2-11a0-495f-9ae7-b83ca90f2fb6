import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  InitializeRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import { logger } from '../utils/logger';
import { getConfig, isClinikoApiConfigured } from '../utils/config';
import { ClinikoApiClient } from '../services/cliniko_api';
import { patientManagementTools } from './tools/patient';
import { appointmentManagementTools } from './tools/appointment';
import { PatientHandlers } from './handlers/patient_handlers';

export class ClinikoMCPServer {
  private server: Server;
  private apiClient: ClinikoApiClient | null = null;
  private patientHandlers: PatientHandlers | null = null;
  private initialized: Promise<void>;

  constructor() {
    logger.info('Initializing Cliniko MCP Server');
    
    const config = getConfig();
    const apiConfigured = isClinikoApiConfigured();
    
    // Initialize API client if configured
    if (apiConfigured) {
      try {
        this.apiClient = new ClinikoApiClient();
        this.patientHandlers = new PatientHandlers(this.apiClient);
        logger.info('Cliniko API client initialized successfully');
      } catch (error) {
        logger.error('Failed to initialize Cliniko API client:', error);
        throw error;
      }
    } else {
      logger.warn('Cliniko API not configured. Please set CLINIKO_API_KEY');
      throw new Error('Cliniko API not configured. Please set CLINIKO_API_KEY');
    }

    // Initialize MCP server
    this.server = new Server(
      {
        name: 'cliniko-mcp',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.initialized = this.initializeServer();
    this.setupHandlers();
  }

  private async initializeServer(): Promise<void> {
    try {
      // Test API connection
      if (this.apiClient) {
        const isConnected = await this.apiClient.testConnection();
        if (!isConnected) {
          logger.error('Failed to connect to Cliniko API');
          throw new Error('Cliniko API connection failed');
        }
        
        const healthInfo = await this.apiClient.healthCheck();
        logger.info('Cliniko API connection verified', healthInfo);
      }

      logger.info('Cliniko MCP Server initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize server:', error);
      throw error;
    }
  }

  private setupHandlers(): void {
    // Handle initialization
    this.server.setRequestHandler(InitializeRequestSchema, async () => {
      const config = getConfig();
      const apiConfigured = isClinikoApiConfigured();
      
      const totalTools = this.getAvailableTools().length;
      
      logger.info(`MCP server initialized with ${totalTools} tools (Cliniko API: ${apiConfigured ? 'configured' : 'not configured'})`);

      return {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: {},
        },
        serverInfo: {
          name: 'cliniko-mcp',
          version: '1.0.0',
        },
      };
    });

    // Handle tool listing
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      const tools = this.getAvailableTools();
      
      logger.debug(`Tool listing: ${tools.length} tools available`);
      
      return { tools };
    });

    // Handle tool execution
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      
      try {
        logger.debug(`Executing tool: ${name}`, { args });
        
        const result = await this.executeTool(name, args);
        
        logger.debug(`Tool ${name} executed successfully`);
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(result, null, 2),
            },
          ],
        };
      } catch (error) {
        logger.error(`Error executing tool ${name}:`, error);
        
        return {
          content: [
            {
              type: 'text',
              text: `Error executing tool ${name}: ${error instanceof Error ? error.message : 'Unknown error'}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  private getAvailableTools() {
    const tools = [];
    
    // Add patient management tools if API is configured
    if (isClinikoApiConfigured()) {
      tools.push(...patientManagementTools);
      tools.push(...appointmentManagementTools);
    }
    
    return tools;
  }

  private async executeTool(name: string, args: any): Promise<any> {
    // Ensure server is initialized
    await this.initialized;

    // Check if API is configured for tools that require it
    if (!this.apiClient) {
      throw new Error('Cliniko API not configured. Please set CLINIKO_API_KEY');
    }

    // Patient management tools
    if (name.startsWith('cliniko_') && 
        (name.includes('patient') || name.includes('medical_alert'))) {
      if (!this.patientHandlers) {
        throw new Error('Patient handlers not initialized');
      }
      return this.patientHandlers.handleTool(name, args);
    }

    // Appointment management tools
    if (name.startsWith('cliniko_') && 
        (name.includes('appointment') || name.includes('attendee') || name.includes('available'))) {
      // TODO: Implement appointment handlers
      throw new Error('Appointment handlers not yet implemented');
    }

    // Practitioner and business tools
    if (name.startsWith('cliniko_') && 
        (name.includes('practitioner') || name.includes('business') || name.includes('appointment_type'))) {
      // TODO: Implement practitioner/business handlers
      throw new Error('Practitioner/business handlers not yet implemented');
    }

    // Invoice and billing tools
    if (name.startsWith('cliniko_') && 
        (name.includes('invoice') || name.includes('billable') || name.includes('product'))) {
      // TODO: Implement invoice handlers
      throw new Error('Invoice handlers not yet implemented');
    }

    // Clinical documentation tools
    if (name.startsWith('cliniko_') && 
        (name.includes('treatment_note') || name.includes('patient_form'))) {
      // TODO: Implement clinical handlers
      throw new Error('Clinical documentation handlers not yet implemented');
    }

    // Reference data tools
    if (name.startsWith('cliniko_') && 
        (name.includes('settings') || name.includes('concession') || name.includes('referral'))) {
      // TODO: Implement reference data handlers
      throw new Error('Reference data handlers not yet implemented');
    }

    throw new Error(`Unknown tool: ${name}`);
  }

  async run(): Promise<void> {
    // Ensure server is initialized
    await this.initialized;
    
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    
    logger.info('Cliniko MCP Server running on stdio transport');
    
    // Keep the process alive
    process.stdin.resume();
  }

  async shutdown(): Promise<void> {
    logger.info('Shutting down Cliniko MCP Server...');
    
    // Close any open connections
    if (this.apiClient) {
      // Add cleanup if needed
    }
    
    logger.info('Cliniko MCP Server shutdown complete');
  }

  // Expose for testing
  public async testConnection(): Promise<boolean> {
    if (!this.apiClient) return false;
    return this.apiClient.testConnection();
  }

  public getApiClient(): ClinikoApiClient | null {
    return this.apiClient;
  }
}
