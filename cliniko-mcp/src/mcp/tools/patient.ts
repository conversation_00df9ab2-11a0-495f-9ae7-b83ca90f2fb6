import { ToolDefinition } from '@modelcontextprotocol/sdk/types.js';

/**
 * Cliniko Patient Management MCP Tools
 * 
 * These tools enable AI agents to manage patients in Cliniko, including
 * creating, updating, searching, and managing patient-related data.
 */
export const patientManagementTools: ToolDefinition[] = [
  {
    name: 'cliniko_list_patients',
    description: `List all patients in Cliniko with optional filtering and pagination. Use this to discover existing patients or search by various criteria.`,
    inputSchema: {
      type: 'object',
      properties: {
        page: {
          type: 'number',
          description: 'Page number for pagination (default: 1)',
          default: 1,
        },
        per_page: {
          type: 'number',
          description: 'Number of patients per page (max: 100, default: 50)',
          default: 50,
          maximum: 100,
        },
        first_name: {
          type: 'string',
          description: 'Filter by first name (supports partial matches with ~ operator)',
        },
        last_name: {
          type: 'string',
          description: 'Filter by last name (supports partial matches with ~ operator)',
        },
        email: {
          type: 'string',
          description: 'Filter by email address',
        },
        phone_number: {
          type: 'string',
          description: 'Filter by phone number',
        },
        mobile_number: {
          type: 'string',
          description: 'Filter by mobile number',
        },
        date_of_birth: {
          type: 'string',
          description: 'Filter by date of birth (YYYY-MM-DD format)',
          pattern: '^\\d{4}-\\d{2}-\\d{2}$',
        },
        created_after: {
          type: 'string',
          description: 'Filter patients created after this date (YYYY-MM-DDTHH:mm:ssZ)',
        },
        updated_after: {
          type: 'string',
          description: 'Filter patients updated after this date (YYYY-MM-DDTHH:mm:ssZ)',
        },
        include_archived: {
          type: 'boolean',
          description: 'Include archived patients in results (default: false)',
          default: false,
        },
        sort: {
          type: 'string',
          description: 'Sort field (e.g., "first_name", "last_name", "created_at")',
          default: 'created_at',
        },
        order: {
          type: 'string',
          enum: ['asc', 'desc'],
          description: 'Sort order (default: desc)',
          default: 'desc',
        },
      },
    },
  },

  {
    name: 'cliniko_get_patient',
    description: `Get detailed information about a specific patient by ID. Returns complete patient record including contact details, medical information, and custom fields.`,
    inputSchema: {
      type: 'object',
      properties: {
        patient_id: {
          type: 'number',
          description: 'The ID of the patient to retrieve',
        },
      },
      required: ['patient_id'],
    },
  },

  {
    name: 'cliniko_search_patients',
    description: `Search for patients using flexible criteria. More powerful than list_patients for finding specific patients by multiple fields.`,
    inputSchema: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'General search query (searches across name, email, phone)',
        },
        first_name: {
          type: 'string',
          description: 'Search by first name (partial matches supported)',
        },
        last_name: {
          type: 'string',
          description: 'Search by last name (partial matches supported)',
        },
        email: {
          type: 'string',
          description: 'Search by email address',
        },
        phone: {
          type: 'string',
          description: 'Search by any phone number (mobile or landline)',
        },
        date_of_birth: {
          type: 'string',
          description: 'Search by date of birth (YYYY-MM-DD)',
        },
        medicare_number: {
          type: 'string',
          description: 'Search by Medicare reference number',
        },
        healthcare_identifier: {
          type: 'string',
          description: 'Search by healthcare identifier',
        },
        limit: {
          type: 'number',
          description: 'Maximum number of results to return (default: 20)',
          default: 20,
          maximum: 100,
        },
      },
    },
  },

  {
    name: 'cliniko_create_patient',
    description: `Create a new patient in Cliniko. Requires first name and last name at minimum. Returns the created patient with assigned ID.`,
    inputSchema: {
      type: 'object',
      properties: {
        first_name: {
          type: 'string',
          description: 'Patient first name (required)',
        },
        last_name: {
          type: 'string',
          description: 'Patient last name (required)',
        },
        preferred_first_name: {
          type: 'string',
          description: 'Preferred first name if different from legal name',
        },
        title: {
          type: 'string',
          enum: ['Mr', 'Mrs', 'Ms', 'Miss', 'Dr', 'Prof', 'Rev'],
          description: 'Patient title',
        },
        date_of_birth: {
          type: 'string',
          description: 'Date of birth (YYYY-MM-DD format)',
          pattern: '^\\d{4}-\\d{2}-\\d{2}$',
        },
        gender: {
          type: 'string',
          enum: ['Male', 'Female', 'Other', 'Prefer not to say'],
          description: 'Patient gender',
        },
        email: {
          type: 'string',
          format: 'email',
          description: 'Primary email address',
        },
        phone_number: {
          type: 'string',
          description: 'Primary phone number',
        },
        mobile_number: {
          type: 'string',
          description: 'Mobile phone number',
        },
        address_1: {
          type: 'string',
          description: 'Street address line 1',
        },
        address_2: {
          type: 'string',
          description: 'Street address line 2',
        },
        address_3: {
          type: 'string',
          description: 'Street address line 3',
        },
        city: {
          type: 'string',
          description: 'City',
        },
        state: {
          type: 'string',
          description: 'State or province',
        },
        post_code: {
          type: 'string',
          description: 'Postal/ZIP code',
        },
        country: {
          type: 'string',
          description: 'Country',
        },
        occupation: {
          type: 'string',
          description: 'Patient occupation',
        },
        emergency_contact: {
          type: 'string',
          description: 'Emergency contact information',
        },
        medicare_reference_number: {
          type: 'string',
          description: 'Medicare reference number',
        },
        healthcare_identifier: {
          type: 'string',
          description: 'Healthcare identifier',
        },
        dva_card_number: {
          type: 'string',
          description: 'DVA card number',
        },
        notes: {
          type: 'string',
          description: 'General notes about the patient',
        },
        time_zone: {
          type: 'string',
          description: 'Patient time zone (IANA format, e.g., "Australia/Sydney")',
        },
        accepted_privacy_policy: {
          type: 'boolean',
          description: 'Whether patient has accepted privacy policy',
        },
        accepted_sms_marketing: {
          type: 'boolean',
          description: 'Whether patient has accepted SMS marketing',
        },
        reminder_type: {
          type: 'string',
          enum: ['SMS', 'Email', 'Phone', 'None'],
          description: 'Preferred reminder type',
        },
        invoice_default_to: {
          type: 'string',
          enum: ['Patient', 'Insurance', 'Other'],
          description: 'Default invoice recipient',
        },
        invoice_email: {
          type: 'string',
          format: 'email',
          description: 'Email for invoices (if different from primary)',
        },
        concession_type_id: {
          type: 'number',
          description: 'ID of concession type if applicable',
        },
        custom_fields: {
          type: 'object',
          description: 'Custom field values as key-value pairs',
          additionalProperties: true,
        },
      },
      required: ['first_name', 'last_name'],
    },
  },

  {
    name: 'cliniko_update_patient',
    description: `Update an existing patient's information. Only provide fields that need to be changed.`,
    inputSchema: {
      type: 'object',
      properties: {
        patient_id: {
          type: 'number',
          description: 'ID of the patient to update',
        },
        first_name: {
          type: 'string',
          description: 'Updated first name',
        },
        last_name: {
          type: 'string',
          description: 'Updated last name',
        },
        preferred_first_name: {
          type: 'string',
          description: 'Updated preferred first name',
        },
        title: {
          type: 'string',
          enum: ['Mr', 'Mrs', 'Ms', 'Miss', 'Dr', 'Prof', 'Rev'],
          description: 'Updated title',
        },
        date_of_birth: {
          type: 'string',
          description: 'Updated date of birth (YYYY-MM-DD)',
          pattern: '^\\d{4}-\\d{2}-\\d{2}$',
        },
        gender: {
          type: 'string',
          enum: ['Male', 'Female', 'Other', 'Prefer not to say'],
          description: 'Updated gender',
        },
        email: {
          type: 'string',
          format: 'email',
          description: 'Updated email address',
        },
        phone_number: {
          type: 'string',
          description: 'Updated phone number',
        },
        mobile_number: {
          type: 'string',
          description: 'Updated mobile number',
        },
        address_1: {
          type: 'string',
          description: 'Updated address line 1',
        },
        address_2: {
          type: 'string',
          description: 'Updated address line 2',
        },
        address_3: {
          type: 'string',
          description: 'Updated address line 3',
        },
        city: {
          type: 'string',
          description: 'Updated city',
        },
        state: {
          type: 'string',
          description: 'Updated state',
        },
        post_code: {
          type: 'string',
          description: 'Updated postal code',
        },
        country: {
          type: 'string',
          description: 'Updated country',
        },
        occupation: {
          type: 'string',
          description: 'Updated occupation',
        },
        emergency_contact: {
          type: 'string',
          description: 'Updated emergency contact',
        },
        notes: {
          type: 'string',
          description: 'Updated notes',
        },
        time_zone: {
          type: 'string',
          description: 'Updated time zone',
        },
        accepted_privacy_policy: {
          type: 'boolean',
          description: 'Updated privacy policy acceptance',
        },
        accepted_sms_marketing: {
          type: 'boolean',
          description: 'Updated SMS marketing acceptance',
        },
        reminder_type: {
          type: 'string',
          enum: ['SMS', 'Email', 'Phone', 'None'],
          description: 'Updated reminder type',
        },
        custom_fields: {
          type: 'object',
          description: 'Updated custom field values',
          additionalProperties: true,
        },
      },
      required: ['patient_id'],
    },
  },

  {
    name: 'cliniko_archive_patient',
    description: `Archive a patient. Archived patients are hidden from normal views but can still be accessed. This is reversible using unarchive.`,
    inputSchema: {
      type: 'object',
      properties: {
        patient_id: {
          type: 'number',
          description: 'ID of the patient to archive',
        },
      },
      required: ['patient_id'],
    },
  },

  {
    name: 'cliniko_unarchive_patient',
    description: `Unarchive a previously archived patient, making them visible in normal views again.`,
    inputSchema: {
      type: 'object',
      properties: {
        patient_id: {
          type: 'number',
          description: 'ID of the patient to unarchive',
        },
      },
      required: ['patient_id'],
    },
  },

  {
    name: 'cliniko_get_patient_medical_alerts',
    description: `Get all medical alerts for a specific patient. Medical alerts are important warnings about patient conditions or allergies.`,
    inputSchema: {
      type: 'object',
      properties: {
        patient_id: {
          type: 'number',
          description: 'ID of the patient to get medical alerts for',
        },
      },
      required: ['patient_id'],
    },
  },

  {
    name: 'cliniko_create_medical_alert',
    description: `Create a new medical alert for a patient. Medical alerts are important warnings displayed prominently in the patient record.`,
    inputSchema: {
      type: 'object',
      properties: {
        patient_id: {
          type: 'number',
          description: 'ID of the patient to create alert for',
        },
        name: {
          type: 'string',
          description: 'Alert name/title (required)',
        },
        description: {
          type: 'string',
          description: 'Detailed description of the alert',
        },
        alert_type: {
          type: 'string',
          description: 'Type of alert (e.g., "Allergy", "Medical Condition", "Warning")',
        },
      },
      required: ['patient_id', 'name'],
    },
  },

  {
    name: 'cliniko_get_patient_attachments',
    description: `Get all file attachments for a specific patient. Attachments can include documents, images, and other files.`,
    inputSchema: {
      type: 'object',
      properties: {
        patient_id: {
          type: 'number',
          description: 'ID of the patient to get attachments for',
        },
      },
      required: ['patient_id'],
    },
  },
];
