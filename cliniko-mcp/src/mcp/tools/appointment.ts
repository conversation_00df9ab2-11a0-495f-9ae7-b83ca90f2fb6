import { ToolDefinition } from '@modelcontextprotocol/sdk/types.js';

/**
 * Cliniko Appointment Management MCP Tools
 * 
 * These tools enable AI agents to manage appointments in Cliniko, including
 * individual appointments, group appointments, attendees, and scheduling.
 */
export const appointmentManagementTools: ToolDefinition[] = [
  {
    name: 'cliniko_list_individual_appointments',
    description: `List individual appointments with optional filtering. Use this to find appointments by date, patient, practitioner, or other criteria.`,
    inputSchema: {
      type: 'object',
      properties: {
        page: {
          type: 'number',
          description: 'Page number for pagination (default: 1)',
          default: 1,
        },
        per_page: {
          type: 'number',
          description: 'Number of appointments per page (max: 100, default: 50)',
          default: 50,
          maximum: 100,
        },
        appointment_start_from: {
          type: 'string',
          description: 'Filter appointments starting from this date/time (YYYY-MM-DDTHH:mm:ssZ)',
        },
        appointment_start_to: {
          type: 'string',
          description: 'Filter appointments starting before this date/time (YYYY-MM-DDTHH:mm:ssZ)',
        },
        patient_id: {
          type: 'number',
          description: 'Filter by specific patient ID',
        },
        practitioner_id: {
          type: 'number',
          description: 'Filter by specific practitioner ID',
        },
        business_id: {
          type: 'number',
          description: 'Filter by specific business ID',
        },
        appointment_type_id: {
          type: 'number',
          description: 'Filter by specific appointment type ID',
        },
        patient_arrived: {
          type: 'boolean',
          description: 'Filter by patient arrival status',
        },
        patient_confirmed: {
          type: 'boolean',
          description: 'Filter by patient confirmation status',
        },
        include_cancelled: {
          type: 'boolean',
          description: 'Include cancelled appointments (default: false)',
          default: false,
        },
        sort: {
          type: 'string',
          description: 'Sort field (default: appointment_start)',
          default: 'appointment_start',
        },
        order: {
          type: 'string',
          enum: ['asc', 'desc'],
          description: 'Sort order (default: asc)',
          default: 'asc',
        },
      },
    },
  },

  {
    name: 'cliniko_get_individual_appointment',
    description: `Get detailed information about a specific individual appointment by ID.`,
    inputSchema: {
      type: 'object',
      properties: {
        appointment_id: {
          type: 'number',
          description: 'The ID of the appointment to retrieve',
        },
      },
      required: ['appointment_id'],
    },
  },

  {
    name: 'cliniko_create_individual_appointment',
    description: `Create a new individual appointment. Requires patient, practitioner, appointment type, business, and time slot.`,
    inputSchema: {
      type: 'object',
      properties: {
        appointment_start: {
          type: 'string',
          description: 'Appointment start time (YYYY-MM-DDTHH:mm:ssZ format)',
          pattern: '^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z$',
        },
        appointment_end: {
          type: 'string',
          description: 'Appointment end time (YYYY-MM-DDTHH:mm:ssZ format)',
          pattern: '^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z$',
        },
        patient_id: {
          type: 'number',
          description: 'ID of the patient for this appointment',
        },
        practitioner_id: {
          type: 'number',
          description: 'ID of the practitioner for this appointment',
        },
        appointment_type_id: {
          type: 'number',
          description: 'ID of the appointment type',
        },
        business_id: {
          type: 'number',
          description: 'ID of the business/location',
        },
        notes: {
          type: 'string',
          description: 'Notes about the appointment',
        },
        patient_case_id: {
          type: 'number',
          description: 'ID of the patient case if applicable',
        },
        repeats: {
          type: 'string',
          enum: ['never', 'daily', 'weekly', 'fortnightly', 'monthly', 'yearly'],
          description: 'Repeat pattern for recurring appointments',
          default: 'never',
        },
        online_booking_policy_accepted: {
          type: 'boolean',
          description: 'Whether online booking policy was accepted',
        },
        check_conflicts: {
          type: 'boolean',
          description: 'Check for scheduling conflicts before creating (default: true)',
          default: true,
        },
      },
      required: [
        'appointment_start',
        'appointment_end',
        'patient_id',
        'practitioner_id',
        'appointment_type_id',
        'business_id'
      ],
    },
  },

  {
    name: 'cliniko_update_individual_appointment',
    description: `Update an existing individual appointment. Only provide fields that need to be changed.`,
    inputSchema: {
      type: 'object',
      properties: {
        appointment_id: {
          type: 'number',
          description: 'ID of the appointment to update',
        },
        appointment_start: {
          type: 'string',
          description: 'Updated appointment start time (YYYY-MM-DDTHH:mm:ssZ)',
        },
        appointment_end: {
          type: 'string',
          description: 'Updated appointment end time (YYYY-MM-DDTHH:mm:ssZ)',
        },
        patient_id: {
          type: 'number',
          description: 'Updated patient ID',
        },
        practitioner_id: {
          type: 'number',
          description: 'Updated practitioner ID',
        },
        appointment_type_id: {
          type: 'number',
          description: 'Updated appointment type ID',
        },
        business_id: {
          type: 'number',
          description: 'Updated business ID',
        },
        notes: {
          type: 'string',
          description: 'Updated notes',
        },
        patient_case_id: {
          type: 'number',
          description: 'Updated patient case ID',
        },
        patient_arrived: {
          type: 'boolean',
          description: 'Mark patient as arrived/not arrived',
        },
        patient_confirmed: {
          type: 'boolean',
          description: 'Mark appointment as confirmed/unconfirmed',
        },
        check_conflicts: {
          type: 'boolean',
          description: 'Check for scheduling conflicts before updating (default: true)',
          default: true,
        },
      },
      required: ['appointment_id'],
    },
  },

  {
    name: 'cliniko_cancel_individual_appointment',
    description: `Cancel an individual appointment with optional cancellation reason and note.`,
    inputSchema: {
      type: 'object',
      properties: {
        appointment_id: {
          type: 'number',
          description: 'ID of the appointment to cancel',
        },
        cancellation_reason_id: {
          type: 'number',
          description: 'ID of the cancellation reason (optional)',
        },
        cancellation_note: {
          type: 'string',
          description: 'Note explaining the cancellation',
        },
      },
      required: ['appointment_id'],
    },
  },

  {
    name: 'cliniko_check_appointment_conflicts',
    description: `Check for scheduling conflicts for a specific appointment time slot and practitioner.`,
    inputSchema: {
      type: 'object',
      properties: {
        appointment_id: {
          type: 'number',
          description: 'ID of existing appointment to check (optional)',
        },
        appointment_start: {
          type: 'string',
          description: 'Appointment start time to check (YYYY-MM-DDTHH:mm:ssZ)',
        },
        appointment_end: {
          type: 'string',
          description: 'Appointment end time to check (YYYY-MM-DDTHH:mm:ssZ)',
        },
        practitioner_id: {
          type: 'number',
          description: 'ID of the practitioner to check conflicts for',
        },
        business_id: {
          type: 'number',
          description: 'ID of the business/location',
        },
      },
      required: ['appointment_start', 'appointment_end', 'practitioner_id', 'business_id'],
    },
  },

  {
    name: 'cliniko_list_group_appointments',
    description: `List group appointments with optional filtering. Group appointments can have multiple attendees.`,
    inputSchema: {
      type: 'object',
      properties: {
        page: {
          type: 'number',
          description: 'Page number for pagination (default: 1)',
          default: 1,
        },
        per_page: {
          type: 'number',
          description: 'Number of appointments per page (max: 100, default: 50)',
          default: 50,
          maximum: 100,
        },
        appointment_start_from: {
          type: 'string',
          description: 'Filter appointments starting from this date/time',
        },
        appointment_start_to: {
          type: 'string',
          description: 'Filter appointments starting before this date/time',
        },
        practitioner_id: {
          type: 'number',
          description: 'Filter by specific practitioner ID',
        },
        business_id: {
          type: 'number',
          description: 'Filter by specific business ID',
        },
        appointment_type_id: {
          type: 'number',
          description: 'Filter by specific appointment type ID',
        },
        include_cancelled: {
          type: 'boolean',
          description: 'Include cancelled appointments (default: false)',
          default: false,
        },
      },
    },
  },

  {
    name: 'cliniko_create_group_appointment',
    description: `Create a new group appointment that can accommodate multiple patients.`,
    inputSchema: {
      type: 'object',
      properties: {
        appointment_start: {
          type: 'string',
          description: 'Appointment start time (YYYY-MM-DDTHH:mm:ssZ format)',
        },
        appointment_end: {
          type: 'string',
          description: 'Appointment end time (YYYY-MM-DDTHH:mm:ssZ format)',
        },
        practitioner_id: {
          type: 'number',
          description: 'ID of the practitioner for this appointment',
        },
        appointment_type_id: {
          type: 'number',
          description: 'ID of the appointment type',
        },
        business_id: {
          type: 'number',
          description: 'ID of the business/location',
        },
        max_attendees: {
          type: 'number',
          description: 'Maximum number of attendees allowed',
          minimum: 1,
        },
        notes: {
          type: 'string',
          description: 'Notes about the group appointment',
        },
        repeats: {
          type: 'string',
          enum: ['never', 'daily', 'weekly', 'fortnightly', 'monthly', 'yearly'],
          description: 'Repeat pattern for recurring appointments',
          default: 'never',
        },
      },
      required: [
        'appointment_start',
        'appointment_end',
        'practitioner_id',
        'appointment_type_id',
        'business_id',
        'max_attendees'
      ],
    },
  },

  {
    name: 'cliniko_list_attendees',
    description: `List attendees for group appointments with optional filtering.`,
    inputSchema: {
      type: 'object',
      properties: {
        group_appointment_id: {
          type: 'number',
          description: 'Filter by specific group appointment ID',
        },
        patient_id: {
          type: 'number',
          description: 'Filter by specific patient ID',
        },
        appointment_start_from: {
          type: 'string',
          description: 'Filter attendees for appointments starting from this date/time',
        },
        appointment_start_to: {
          type: 'string',
          description: 'Filter attendees for appointments starting before this date/time',
        },
        patient_arrived: {
          type: 'boolean',
          description: 'Filter by patient arrival status',
        },
        include_cancelled: {
          type: 'boolean',
          description: 'Include cancelled attendees (default: false)',
          default: false,
        },
        page: {
          type: 'number',
          description: 'Page number for pagination (default: 1)',
          default: 1,
        },
        per_page: {
          type: 'number',
          description: 'Number of attendees per page (max: 100, default: 50)',
          default: 50,
          maximum: 100,
        },
      },
    },
  },

  {
    name: 'cliniko_create_attendee',
    description: `Add a patient as an attendee to a group appointment.`,
    inputSchema: {
      type: 'object',
      properties: {
        group_appointment_id: {
          type: 'number',
          description: 'ID of the group appointment to add attendee to',
        },
        patient_id: {
          type: 'number',
          description: 'ID of the patient to add as attendee',
        },
        notes: {
          type: 'string',
          description: 'Notes specific to this attendee',
        },
        online_booking_policy_accepted: {
          type: 'boolean',
          description: 'Whether online booking policy was accepted',
        },
      },
      required: ['group_appointment_id', 'patient_id'],
    },
  },

  {
    name: 'cliniko_get_available_times',
    description: `Get available appointment times for a specific practitioner, appointment type, and business within a date range.`,
    inputSchema: {
      type: 'object',
      properties: {
        business_id: {
          type: 'number',
          description: 'ID of the business to check availability for',
        },
        practitioner_id: {
          type: 'number',
          description: 'ID of the practitioner to check availability for',
        },
        appointment_type_id: {
          type: 'number',
          description: 'ID of the appointment type',
        },
        from: {
          type: 'string',
          description: 'Start date for availability search (YYYY-MM-DD)',
          pattern: '^\\d{4}-\\d{2}-\\d{2}$',
        },
        to: {
          type: 'string',
          description: 'End date for availability search (YYYY-MM-DD)',
          pattern: '^\\d{4}-\\d{2}-\\d{2}$',
        },
      },
      required: ['business_id', 'practitioner_id', 'appointment_type_id', 'from', 'to'],
    },
  },

  {
    name: 'cliniko_get_next_available_time',
    description: `Get the next available appointment time for a specific practitioner, appointment type, and business.`,
    inputSchema: {
      type: 'object',
      properties: {
        business_id: {
          type: 'number',
          description: 'ID of the business to check availability for',
        },
        practitioner_id: {
          type: 'number',
          description: 'ID of the practitioner to check availability for',
        },
        appointment_type_id: {
          type: 'number',
          description: 'ID of the appointment type',
        },
        from: {
          type: 'string',
          description: 'Start searching from this date (YYYY-MM-DD, default: today)',
        },
      },
      required: ['business_id', 'practitioner_id', 'appointment_type_id'],
    },
  },
];
