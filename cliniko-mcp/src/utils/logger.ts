import { createWriteStream, existsSync, mkdirSync } from 'fs';
import { dirname } from 'path';

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
}

class Logger {
  private logLevel: LogLevel;
  private logFile?: NodeJS.WritableStream;
  private disableConsole: boolean;

  constructor() {
    this.logLevel = this.parseLogLevel(process.env.LOG_LEVEL || 'error');
    this.disableConsole = process.env.DISABLE_CONSOLE_OUTPUT === 'true';
    
    // Initialize log file if needed
    const logPath = process.env.CLINIKO_MCP_LOG_PATH;
    if (logPath) {
      this.initLogFile(logPath);
    }
  }

  private parseLogLevel(level: string): LogLevel {
    switch (level.toLowerCase()) {
      case 'debug': return LogLevel.DEBUG;
      case 'info': return LogLevel.INFO;
      case 'warn': return LogLevel.WARN;
      case 'error': return LogLevel.ERROR;
      default: return LogLevel.ERROR;
    }
  }

  private initLogFile(logPath: string): void {
    try {
      const logDir = dirname(logPath);
      if (!existsSync(logDir)) {
        mkdirSync(logDir, { recursive: true });
      }
      this.logFile = createWriteStream(logPath, { flags: 'a' });
    } catch (error) {
      // Fallback to console if file logging fails
      console.error('Failed to initialize log file:', error);
    }
  }

  private log(level: LogLevel, message: string, ...args: any[]): void {
    if (level > this.logLevel) return;

    const timestamp = new Date().toISOString();
    const levelName = LogLevel[level];
    const formattedMessage = `[${timestamp}] ${levelName}: ${message}`;
    
    // Format additional arguments
    const formattedArgs = args.length > 0 ? ' ' + args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ') : '';

    const fullMessage = formattedMessage + formattedArgs;

    // Write to log file if available
    if (this.logFile) {
      this.logFile.write(fullMessage + '\n');
    }

    // Write to console if not disabled and not in stdio mode
    if (!this.disableConsole && process.env.CLINIKO_MCP_MODE !== 'stdio') {
      switch (level) {
        case LogLevel.ERROR:
          console.error(fullMessage);
          break;
        case LogLevel.WARN:
          console.warn(fullMessage);
          break;
        case LogLevel.INFO:
          console.info(fullMessage);
          break;
        case LogLevel.DEBUG:
          console.debug(fullMessage);
          break;
      }
    }
  }

  error(message: string, ...args: any[]): void {
    this.log(LogLevel.ERROR, message, ...args);
  }

  warn(message: string, ...args: any[]): void {
    this.log(LogLevel.WARN, message, ...args);
  }

  info(message: string, ...args: any[]): void {
    this.log(LogLevel.INFO, message, ...args);
  }

  debug(message: string, ...args: any[]): void {
    this.log(LogLevel.DEBUG, message, ...args);
  }

  close(): void {
    if (this.logFile) {
      this.logFile.end();
    }
  }
}

export const logger = new Logger();
