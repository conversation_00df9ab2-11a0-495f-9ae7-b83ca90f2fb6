import { config } from 'dotenv';
import { logger } from './logger';

// Load environment variables
config();

export interface ClinikoMCPConfig {
  // MCP Server Configuration
  mode: 'stdio' | 'http';
  logLevel: string;
  disableConsoleOutput: boolean;

  // Cliniko API Configuration
  apiKey?: string;
  shard?: string;
  autoDetectShard: boolean;
  userAgent: string;

  // Rate Limiting
  rateLimit: number;
  rateLimitWindow: number;

  // Request Configuration
  requestTimeout: number;
  retryAttempts: number;
  retryDelay: number;

  // HTTP Server Settings (for http mode)
  httpPort: number;
  httpHost: string;

  // Cache Settings
  cacheTTL: number;
  enableCache: boolean;

  // Validation Settings
  strictValidation: boolean;
  validateResponses: boolean;

  // Development Settings
  debugApiCalls: boolean;
  mockApiResponses: boolean;
}

class ConfigManager {
  private config: ClinikoMCPConfig;

  constructor() {
    this.config = this.loadConfig();
    this.validateConfig();
  }

  private loadConfig(): ClinikoMCPConfig {
    return {
      // MCP Server Configuration
      mode: (process.env.CLINIKO_MCP_MODE as 'stdio' | 'http') || 'stdio',
      logLevel: process.env.LOG_LEVEL || 'error',
      disableConsoleOutput: process.env.DISABLE_CONSOLE_OUTPUT === 'true',

      // Cliniko API Configuration
      apiKey: process.env.CLINIKO_API_KEY,
      shard: process.env.CLINIKO_SHARD,
      autoDetectShard: process.env.CLINIKO_AUTO_DETECT_SHARD !== 'false',
      userAgent: process.env.CLINIKO_USER_AGENT || 'Cliniko MCP Server (<EMAIL>)',

      // Rate Limiting
      rateLimit: parseInt(process.env.CLINIKO_RATE_LIMIT || '200'),
      rateLimitWindow: parseInt(process.env.CLINIKO_RATE_LIMIT_WINDOW || '60000'),

      // Request Configuration
      requestTimeout: parseInt(process.env.CLINIKO_REQUEST_TIMEOUT || '30000'),
      retryAttempts: parseInt(process.env.CLINIKO_RETRY_ATTEMPTS || '3'),
      retryDelay: parseInt(process.env.CLINIKO_RETRY_DELAY || '1000'),

      // HTTP Server Settings
      httpPort: parseInt(process.env.HTTP_PORT || '3000'),
      httpHost: process.env.HTTP_HOST || 'localhost',

      // Cache Settings
      cacheTTL: parseInt(process.env.CACHE_TTL || '300000'),
      enableCache: process.env.ENABLE_CACHE !== 'false',

      // Validation Settings
      strictValidation: process.env.STRICT_VALIDATION !== 'false',
      validateResponses: process.env.VALIDATE_RESPONSES !== 'false',

      // Development Settings
      debugApiCalls: process.env.DEBUG_API_CALLS === 'true',
      mockApiResponses: process.env.MOCK_API_RESPONSES === 'true',
    };
  }

  private validateConfig(): void {
    // Validate mode
    if (!['stdio', 'http'].includes(this.config.mode)) {
      throw new Error(`Invalid CLINIKO_MCP_MODE: ${this.config.mode}. Must be 'stdio' or 'http'`);
    }

    // Validate numeric values
    if (this.config.rateLimit < 1) {
      throw new Error('CLINIKO_RATE_LIMIT must be at least 1');
    }

    if (this.config.rateLimitWindow < 1000) {
      throw new Error('CLINIKO_RATE_LIMIT_WINDOW must be at least 1000ms');
    }

    if (this.config.requestTimeout < 1000) {
      throw new Error('CLINIKO_REQUEST_TIMEOUT must be at least 1000ms');
    }

    if (this.config.retryAttempts < 0) {
      throw new Error('CLINIKO_RETRY_ATTEMPTS must be 0 or greater');
    }

    if (this.config.httpPort < 1 || this.config.httpPort > 65535) {
      throw new Error('HTTP_PORT must be between 1 and 65535');
    }

    // Validate user agent format
    if (!this.config.userAgent.includes('(') || !this.config.userAgent.includes('@')) {
      logger.warn('User agent should include app name and contact email: "App Name (<EMAIL>)"');
    }

    // Log configuration status
    logger.info('Configuration loaded successfully', {
      mode: this.config.mode,
      hasApiKey: !!this.config.apiKey,
      shard: this.config.shard || 'auto-detect',
      rateLimit: this.config.rateLimit,
    });
  }

  get(): ClinikoMCPConfig {
    return { ...this.config };
  }

  isClinikoApiConfigured(): boolean {
    return !!this.config.apiKey;
  }

  getClinikoApiConfig(): { apiKey: string; shard?: string; userAgent: string } | null {
    if (!this.isClinikoApiConfigured()) {
      return null;
    }
    return {
      apiKey: this.config.apiKey!,
      shard: this.config.shard,
      userAgent: this.config.userAgent,
    };
  }

  detectShardFromApiKey(apiKey: string): string | null {
    // Cliniko API keys often have shard suffixes like "-au1", "-au2", "-us1"
    const shardMatch = apiKey.match(/-([a-z]{2}\d+)$/);
    if (shardMatch) {
      return shardMatch[1];
    }
    return null;
  }

  getBaseUrl(): string {
    const apiConfig = this.getClinikoApiConfig();
    if (!apiConfig) {
      throw new Error('Cliniko API not configured');
    }

    let shard = apiConfig.shard;
    
    // Auto-detect shard if not specified
    if (!shard && this.config.autoDetectShard) {
      shard = this.detectShardFromApiKey(apiConfig.apiKey);
    }

    // Default to au1 if no shard detected
    if (!shard) {
      shard = 'au1';
      logger.warn('No shard specified or detected, defaulting to au1');
    }

    return `https://api.${shard}.cliniko.com/v1`;
  }

  update(updates: Partial<ClinikoMCPConfig>): void {
    this.config = { ...this.config, ...updates };
    this.validateConfig();
  }
}

export const configManager = new ConfigManager();
export const getConfig = () => configManager.get();
export const isClinikoApiConfigured = () => configManager.isClinikoApiConfigured();
export const getClinikoApiConfig = () => configManager.getClinikoApiConfig();
export const getBaseUrl = () => configManager.getBaseUrl();
