import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import { logger } from '../utils/logger';
import { getConfig, getClinikoApiConfig, getBaseUrl } from '../utils/config';
import { 
  ClinikoApiError, 
  ClinikoRateLimitError, 
  ClinikoListResponse,
  ClinikoFilterOptions 
} from '../models/common';

export class ClinikoApiClient {
  private client: AxiosInstance;
  private baseUrl: string;
  private apiKey: string;
  private userAgent: string;
  private rateLimitRemaining: number = 200;
  private rateLimitReset: number = 0;

  constructor() {
    const config = getConfig();
    const apiConfig = getClinikoApiConfig();
    
    if (!apiConfig) {
      throw new Error('Cliniko API not configured. Please set CLINIKO_API_KEY');
    }

    this.baseUrl = getBaseUrl();
    this.apiKey = apiConfig.apiKey;
    this.userAgent = apiConfig.userAgent;

    this.client = axios.create({
      baseURL: this.baseUrl,
      timeout: config.requestTimeout,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'User-Agent': this.userAgent,
      },
      auth: {
        username: this.apiKey,
        password: '', // Cliniko uses API key as username, no password
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    const config = getConfig();

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        if (getConfig().debugApiCalls) {
          logger.debug(`API Request: ${config.method?.toUpperCase()} ${config.url}`, {
            params: config.params,
            data: config.data,
          });
        }
        return config;
      },
      (error) => {
        logger.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        // Update rate limit info from headers
        this.updateRateLimitInfo(response);

        if (getConfig().debugApiCalls) {
          logger.debug(`API Response: ${response.status} ${response.config.url}`, {
            data: response.data,
            rateLimitRemaining: this.rateLimitRemaining,
          });
        }
        return response;
      },
      async (error: AxiosError) => {
        // Update rate limit info if available
        if (error.response) {
          this.updateRateLimitInfo(error.response);
        }

        const clinikoError = this.handleApiError(error);
        
        // Retry logic for certain errors
        if (this.shouldRetry(error) && config.retryAttempts > 0) {
          return this.retryRequest(error);
        }

        throw clinikoError;
      }
    );
  }

  private updateRateLimitInfo(response: AxiosResponse): void {
    const remaining = response.headers['x-ratelimit-remaining'];
    const reset = response.headers['x-ratelimit-reset'];

    if (remaining !== undefined) {
      this.rateLimitRemaining = parseInt(remaining, 10);
    }

    if (reset !== undefined) {
      this.rateLimitReset = parseInt(reset, 10);
    }
  }

  private handleApiError(error: AxiosError): ClinikoApiError {
    const status = error.response?.status;
    const data = error.response?.data as any;

    let message = error.message;
    let errors: any[] = [];

    if (data?.errors) {
      errors = Array.isArray(data.errors) ? data.errors : [data.errors];
      message = errors.map(e => e.message || e).join(', ');
    } else if (data?.error) {
      message = data.error;
    }

    // Handle rate limiting
    if (status === 429) {
      const resetTime = this.rateLimitReset * 1000; // Convert to milliseconds
      return new ClinikoRateLimitError(
        `Rate limit exceeded. Reset at ${new Date(resetTime).toISOString()}`,
        resetTime
      );
    }

    logger.error(`Cliniko API Error: ${status} ${message}`, {
      url: error.config?.url,
      method: error.config?.method,
      errors,
    });

    return new ClinikoApiError(message, status, errors, data);
  }

  private shouldRetry(error: AxiosError): boolean {
    const status = error.response?.status;
    
    // Retry on network errors or 5xx server errors
    return !status || status >= 500;
  }

  private async retryRequest(error: AxiosError): Promise<AxiosResponse> {
    const config = getConfig();
    const retryConfig = { ...error.config };
    
    // Add retry metadata
    retryConfig.metadata = retryConfig.metadata || {};
    retryConfig.metadata.retryCount = (retryConfig.metadata.retryCount || 0) + 1;

    if (retryConfig.metadata.retryCount > config.retryAttempts) {
      throw this.handleApiError(error);
    }

    // Wait before retrying
    await new Promise(resolve => setTimeout(resolve, config.retryDelay));

    logger.warn(`Retrying request (attempt ${retryConfig.metadata.retryCount}/${config.retryAttempts}): ${retryConfig.url}`);

    return this.client.request(retryConfig);
  }

  // Generic CRUD operations
  async get<T>(endpoint: string, options?: ClinikoFilterOptions): Promise<T> {
    const params = this.buildQueryParams(options);
    const response = await this.client.get<T>(endpoint, { params });
    return response.data;
  }

  async post<T>(endpoint: string, data: any): Promise<T> {
    const response = await this.client.post<T>(endpoint, data);
    return response.data;
  }

  async patch<T>(endpoint: string, data: any): Promise<T> {
    const response = await this.client.patch<T>(endpoint, data);
    return response.data;
  }

  async delete(endpoint: string): Promise<void> {
    await this.client.delete(endpoint);
  }

  // List operations with pagination
  async list<T>(endpoint: string, options?: ClinikoFilterOptions): Promise<ClinikoListResponse<T>> {
    return this.get<ClinikoListResponse<T>>(endpoint, options);
  }

  // Archive operations
  async archive(endpoint: string): Promise<void> {
    await this.post(`${endpoint}/archive`, {});
  }

  async unarchive(endpoint: string): Promise<void> {
    await this.post(`${endpoint}/unarchive`, {});
  }

  // Cancel operations (for appointments)
  async cancel(endpoint: string, data?: any): Promise<void> {
    await this.client.patch(`${endpoint}/cancel`, data || {});
  }

  private buildQueryParams(options?: ClinikoFilterOptions): Record<string, any> {
    if (!options) return {};

    const params: Record<string, any> = {};

    if (options.page !== undefined) {
      params.page = options.page;
    }

    if (options.per_page !== undefined) {
      params.per_page = Math.min(options.per_page, 100); // Cliniko max is 100
    }

    if (options.q && options.q.length > 0) {
      params['q[]'] = options.q;
    }

    if (options.sort) {
      params.sort = options.sort;
    }

    if (options.order) {
      params.order = options.order;
    }

    return params;
  }

  // Health check
  async healthCheck(): Promise<{ status: string; shard: string }> {
    try {
      // Use the user endpoint as a health check
      await this.get('/user');
      const shard = this.baseUrl.match(/api\.([^.]+)\.cliniko\.com/)?.[1] || 'unknown';
      return { status: 'healthy', shard };
    } catch (error) {
      logger.error('Cliniko API health check failed:', error);
      throw new ClinikoApiError('Cliniko API health check failed');
    }
  }

  // Rate limit info
  getRateLimitInfo(): { remaining: number; reset: number } {
    return {
      remaining: this.rateLimitRemaining,
      reset: this.rateLimitReset,
    };
  }

  // Utility methods
  getBaseUrl(): string {
    return this.baseUrl;
  }

  getApiKey(): string {
    return this.apiKey;
  }

  getUserAgent(): string {
    return this.userAgent;
  }

  // Test connection
  async testConnection(): Promise<boolean> {
    try {
      await this.healthCheck();
      return true;
    } catch (error) {
      return false;
    }
  }
}
