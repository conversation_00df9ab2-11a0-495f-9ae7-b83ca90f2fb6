import { ClinikoResource, <PERSON><PERSON><PERSON>ddress, <PERSON>linikoContact, ClinikoDateString, ClinikoLinks } from './common';

export interface Patient extends ClinikoResource {
  accepted_privacy_policy?: boolean | null;
  accepted_sms_marketing?: boolean | null;
  address_1?: string;
  address_2?: string;
  address_3?: string;
  city?: string;
  country?: string;
  date_of_birth?: ClinikoDateString;
  email?: string;
  emergency_contact?: string;
  first_name: string;
  gender?: string;
  invoice_default_to?: string;
  invoice_email?: string;
  invoice_extra_information?: string;
  last_name: string;
  medicare_reference_number?: string;
  mobile_number?: string;
  notes?: string;
  occupation?: string;
  old_reference_id?: string;
  phone_number?: string;
  post_code?: string;
  preferred_first_name?: string;
  referral_source?: string;
  reminder_type?: string;
  state?: string;
  time_zone?: string;
  title?: string;
  dva_card_number?: string;
  healthcare_identifier?: string;
  concession_type?: {
    id: number;
    name: string;
  };
  patient_phone_numbers?: Array<{
    phone_number: string;
    phone_type: string;
  }>;
  custom_fields?: Record<string, any>;
  links: ClinikoLinks;
}

export interface PatientCreate {
  first_name: string;
  last_name: string;
  accepted_privacy_policy?: boolean;
  accepted_sms_marketing?: boolean;
  address_1?: string;
  address_2?: string;
  address_3?: string;
  city?: string;
  country?: string;
  date_of_birth?: ClinikoDateString;
  email?: string;
  emergency_contact?: string;
  gender?: string;
  invoice_default_to?: string;
  invoice_email?: string;
  invoice_extra_information?: string;
  medicare_reference_number?: string;
  mobile_number?: string;
  notes?: string;
  occupation?: string;
  old_reference_id?: string;
  phone_number?: string;
  post_code?: string;
  preferred_first_name?: string;
  referral_source?: string;
  reminder_type?: string;
  state?: string;
  time_zone?: string;
  title?: string;
  dva_card_number?: string;
  healthcare_identifier?: string;
  concession_type_id?: number;
  patient_phone_numbers?: Array<{
    phone_number: string;
    phone_type: string;
  }>;
  custom_fields?: Record<string, any>;
}

export interface PatientUpdate {
  first_name?: string;
  last_name?: string;
  accepted_privacy_policy?: boolean;
  accepted_sms_marketing?: boolean;
  address_1?: string;
  address_2?: string;
  address_3?: string;
  city?: string;
  country?: string;
  date_of_birth?: ClinikoDateString;
  email?: string;
  emergency_contact?: string;
  gender?: string;
  invoice_default_to?: string;
  invoice_email?: string;
  invoice_extra_information?: string;
  medicare_reference_number?: string;
  mobile_number?: string;
  notes?: string;
  occupation?: string;
  old_reference_id?: string;
  phone_number?: string;
  post_code?: string;
  preferred_first_name?: string;
  referral_source?: string;
  reminder_type?: string;
  state?: string;
  time_zone?: string;
  title?: string;
  dva_card_number?: string;
  healthcare_identifier?: string;
  concession_type_id?: number;
  patient_phone_numbers?: Array<{
    phone_number: string;
    phone_type: string;
  }>;
  custom_fields?: Record<string, any>;
}

export interface PatientSearchOptions {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone_number?: string;
  mobile_number?: string;
  date_of_birth?: ClinikoDateString;
  medicare_reference_number?: string;
  healthcare_identifier?: string;
  old_reference_id?: string;
  created_at?: string;
  updated_at?: string;
  archived_at?: string;
}

// Patient-related resources
export interface MedicalAlert extends ClinikoResource {
  name: string;
  description?: string;
  alert_type?: string;
  patient: {
    id: number;
    first_name: string;
    last_name: string;
  };
  links: ClinikoLinks;
}

export interface PatientAttachment extends ClinikoResource {
  description?: string;
  file_name: string;
  file_size: number;
  file_type: string;
  url: string;
  patient: {
    id: number;
    first_name: string;
    last_name: string;
  };
  patient_case?: {
    id: number;
    name: string;
  };
  links: ClinikoLinks;
}

export interface PatientCase extends ClinikoResource {
  name: string;
  description?: string;
  status?: string;
  patient: {
    id: number;
    first_name: string;
    last_name: string;
  };
  practitioner?: {
    id: number;
    first_name: string;
    last_name: string;
  };
  links: ClinikoLinks;
}

export interface Relationship extends ClinikoResource {
  relationship_type: string;
  patient: {
    id: number;
    first_name: string;
    last_name: string;
  };
  related_patient: {
    id: number;
    first_name: string;
    last_name: string;
  };
  links: ClinikoLinks;
}

// Validation helpers
export const PATIENT_GENDERS = [
  'Male',
  'Female',
  'Other',
  'Prefer not to say'
] as const;

export const PATIENT_TITLES = [
  'Mr',
  'Mrs',
  'Ms',
  'Miss',
  'Dr',
  'Prof',
  'Rev'
] as const;

export const REMINDER_TYPES = [
  'SMS',
  'Email',
  'Phone',
  'None'
] as const;

export const INVOICE_DEFAULT_TO = [
  'Patient',
  'Insurance',
  'Other'
] as const;

export const PHONE_TYPES = [
  'Home',
  'Work',
  'Mobile',
  'Fax',
  'Other'
] as const;

export type PatientGender = typeof PATIENT_GENDERS[number];
export type PatientTitle = typeof PATIENT_TITLES[number];
export type ReminderType = typeof REMINDER_TYPES[number];
export type InvoiceDefaultTo = typeof INVOICE_DEFAULT_TO[number];
export type PhoneType = typeof PHONE_TYPES[number];
