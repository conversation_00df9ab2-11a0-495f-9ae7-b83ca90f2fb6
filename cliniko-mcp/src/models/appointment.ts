import { ClinikoResource, ClinikoDateTimeString, ClinikoLinks } from './common';

export interface BaseAppointment extends ClinikoResource {
  appointment_end: ClinikoDateTimeString;
  appointment_start: ClinikoDateTimeString;
  appointment_type: {
    id: number;
    name: string;
  };
  business: {
    id: number;
    name: string;
  };
  notes?: string;
  online_booking_policy_accepted?: boolean;
  patient_arrived?: boolean;
  patient_confirmed?: boolean;
  practitioner: {
    id: number;
    first_name: string;
    last_name: string;
  };
  repeated_from?: {
    id: number;
  };
  repeats?: string;
  cancellation_note?: string;
  cancellation_reason?: {
    id: number;
    name: string;
  };
  cancelled_at?: ClinikoDateTimeString;
  did_not_arrive?: boolean;
  email_reminder_sent?: boolean;
  invoice_status?: string;
  last_reminder_sent_at?: ClinikoDateTimeString;
  max_attendees?: number;
  sms_reminder_sent?: boolean;
  treatment_note_status?: string;
  links: ClinikoLinks;
}

export interface IndividualAppointment extends BaseAppointment {
  patient: {
    id: number;
    first_name: string;
    last_name: string;
  };
  patient_case?: {
    id: number;
    name: string;
  };
}

export interface GroupAppointment extends BaseAppointment {
  max_attendees: number;
  attendees?: Attendee[];
}

export interface Attendee extends ClinikoResource {
  appointment_end: ClinikoDateTimeString;
  appointment_start: ClinikoDateTimeString;
  booking_ip_address?: string;
  cancellation_note?: string;
  cancellation_reason?: {
    id: number;
    name: string;
  };
  cancelled_at?: ClinikoDateTimeString;
  email_reminder_sent?: boolean;
  invoice_status?: string;
  notes?: string;
  online_booking_policy_accepted?: boolean;
  patient: {
    id: number;
    first_name: string;
    last_name: string;
  };
  patient_arrived?: boolean;
  patient_confirmed?: boolean;
  sms_reminder_sent?: boolean;
  treatment_note_status?: string;
  group_appointment: {
    id: number;
    appointment_start: ClinikoDateTimeString;
    appointment_end: ClinikoDateTimeString;
  };
  links: ClinikoLinks;
}

export interface AppointmentType extends ClinikoResource {
  name: string;
  description?: string;
  duration_in_minutes: number;
  color?: string;
  show_in_online_bookings?: boolean;
  max_attendees?: number;
  send_confirmation_email?: boolean;
  send_reminder_email?: boolean;
  send_reminder_sms?: boolean;
  links: ClinikoLinks;
}

export interface AvailableTime {
  appointment_start: ClinikoDateTimeString;
  appointment_end: ClinikoDateTimeString;
}

export interface AppointmentConflict {
  conflicting_appointment: {
    id: number;
    appointment_start: ClinikoDateTimeString;
    appointment_end: ClinikoDateTimeString;
    patient?: {
      id: number;
      first_name: string;
      last_name: string;
    };
  };
  conflict_type: string;
}

// Create/Update interfaces
export interface IndividualAppointmentCreate {
  appointment_start: ClinikoDateTimeString;
  appointment_end: ClinikoDateTimeString;
  appointment_type_id: number;
  business_id: number;
  patient_id: number;
  practitioner_id: number;
  notes?: string;
  patient_case_id?: number;
  repeats?: string;
  online_booking_policy_accepted?: boolean;
}

export interface GroupAppointmentCreate {
  appointment_start: ClinikoDateTimeString;
  appointment_end: ClinikoDateTimeString;
  appointment_type_id: number;
  business_id: number;
  practitioner_id: number;
  max_attendees: number;
  notes?: string;
  repeats?: string;
}

export interface AttendeeCreate {
  group_appointment_id: number;
  patient_id: number;
  notes?: string;
  online_booking_policy_accepted?: boolean;
}

export interface IndividualAppointmentUpdate {
  appointment_start?: ClinikoDateTimeString;
  appointment_end?: ClinikoDateTimeString;
  appointment_type_id?: number;
  business_id?: number;
  patient_id?: number;
  practitioner_id?: number;
  notes?: string;
  patient_case_id?: number;
  patient_arrived?: boolean;
  patient_confirmed?: boolean;
}

export interface GroupAppointmentUpdate {
  appointment_start?: ClinikoDateTimeString;
  appointment_end?: ClinikoDateTimeString;
  appointment_type_id?: number;
  business_id?: number;
  practitioner_id?: number;
  max_attendees?: number;
  notes?: string;
  patient_arrived?: boolean;
  patient_confirmed?: boolean;
}

export interface AttendeeUpdate {
  notes?: string;
  patient_arrived?: boolean;
  patient_confirmed?: boolean;
}

export interface AppointmentCancellation {
  cancellation_reason_id?: number;
  cancellation_note?: string;
}

// Search and filter options
export interface AppointmentSearchOptions {
  appointment_start?: string;
  appointment_end?: string;
  patient_id?: number;
  practitioner_id?: number;
  business_id?: number;
  appointment_type_id?: number;
  created_at?: string;
  updated_at?: string;
  cancelled_at?: string;
  patient_arrived?: boolean;
  patient_confirmed?: boolean;
}

// Constants
export const APPOINTMENT_STATUSES = [
  'confirmed',
  'cancelled',
  'arrived',
  'completed',
  'no_show'
] as const;

export const INVOICE_STATUSES = [
  'pending',
  'invoiced',
  'paid',
  'cancelled'
] as const;

export const TREATMENT_NOTE_STATUSES = [
  'pending',
  'completed',
  'locked'
] as const;

export const REPEAT_OPTIONS = [
  'never',
  'daily',
  'weekly',
  'fortnightly',
  'monthly',
  'yearly'
] as const;

export type AppointmentStatus = typeof APPOINTMENT_STATUSES[number];
export type InvoiceStatus = typeof INVOICE_STATUSES[number];
export type TreatmentNoteStatus = typeof TREATMENT_NOTE_STATUSES[number];
export type RepeatOption = typeof REPEAT_OPTIONS[number];
