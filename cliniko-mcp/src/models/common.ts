// Common types and interfaces used across the Cliniko API

export interface ClinikoResource {
  id: number;
  created_at: string;
  updated_at: string;
  archived_at?: string | null;
}

export interface ClinikoListResponse<T> {
  [key: string]: T[];
  total_entries: number;
  links: {
    self: string;
    next?: string;
    previous?: string;
  };
}

export interface ClinikoError {
  errors: Array<{
    field?: string;
    message: string;
    code?: string;
  }>;
}

export interface ClinikoFilterOptions {
  page?: number;
  per_page?: number;
  q?: string[];
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface ClinikoAddress {
  address_1?: string;
  address_2?: string;
  address_3?: string;
  city?: string;
  state?: string;
  post_code?: string;
  country?: string;
}

export interface ClinikoContact {
  email?: string;
  phone_number?: string;
  mobile_number?: string;
}

export interface ClinikoLinks {
  self: string;
  [key: string]: string;
}

// Common filter operators
export type FilterOperator = 
  | '=' | '!=' | '>' | '>=' | '<' | '<=' 
  | '~' | '~~' | '?' | '!?' | '*';

export interface FilterCondition {
  field: string;
  operator: FilterOperator;
  value: string | number | boolean;
}

// Pagination helpers
export interface PaginationOptions {
  page?: number;
  per_page?: number;
}

export interface PaginationInfo {
  current_page: number;
  per_page: number;
  total_entries: number;
  total_pages: number;
  has_next: boolean;
  has_previous: boolean;
}

// Common validation schemas
export const CLINIKO_DATE_FORMAT = /^\d{4}-\d{2}-\d{2}$/;
export const CLINIKO_DATETIME_FORMAT = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/;
export const CLINIKO_EMAIL_FORMAT = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
export const CLINIKO_PHONE_FORMAT = /^[\d\s\-\+\(\)]+$/;

// Common field types
export type ClinikoDateString = string; // YYYY-MM-DD
export type ClinikoDateTimeString = string; // YYYY-MM-DDTHH:mm:ssZ
export type ClinikoEmailString = string;
export type ClinikoPhoneString = string;

// API response status codes
export enum ClinikoStatusCode {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  UNPROCESSABLE_ENTITY = 422,
  TOO_MANY_REQUESTS = 429,
  INTERNAL_SERVER_ERROR = 500,
}

// Common error types
export class ClinikoApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public errors?: ClinikoError['errors'],
    public response?: any
  ) {
    super(message);
    this.name = 'ClinikoApiError';
  }
}

export class ClinikoValidationError extends Error {
  constructor(
    message: string,
    public field?: string,
    public value?: any
  ) {
    super(message);
    this.name = 'ClinikoValidationError';
  }
}

export class ClinikoRateLimitError extends Error {
  constructor(
    message: string,
    public resetTime?: number
  ) {
    super(message);
    this.name = 'ClinikoRateLimitError';
  }
}

// Utility types
export type Partial<T> = {
  [P in keyof T]?: T[P];
};

export type Required<T, K extends keyof T> = T & {
  [P in K]-?: T[P];
};

export type Optional<T, K extends keyof T> = Omit<T, K> & {
  [P in K]?: T[P];
};

// Helper functions
export function buildFilterQuery(conditions: FilterCondition[]): string[] {
  return conditions.map(condition => 
    `${condition.field}:${condition.operator}${condition.value}`
  );
}

export function validateEmail(email: string): boolean {
  return CLINIKO_EMAIL_FORMAT.test(email);
}

export function validatePhone(phone: string): boolean {
  return CLINIKO_PHONE_FORMAT.test(phone);
}

export function validateDate(date: string): boolean {
  return CLINIKO_DATE_FORMAT.test(date);
}

export function validateDateTime(dateTime: string): boolean {
  return CLINIKO_DATETIME_FORMAT.test(dateTime);
}

export function formatDate(date: Date): ClinikoDateString {
  return date.toISOString().split('T')[0];
}

export function formatDateTime(date: Date): ClinikoDateTimeString {
  return date.toISOString();
}

export function parseClinikoDate(dateString: ClinikoDateString): Date {
  return new Date(dateString + 'T00:00:00Z');
}

export function parseClinikoDateTime(dateTimeString: ClinikoDateTimeString): Date {
  return new Date(dateTimeString);
}
