import express from 'express';
import { ClinikoMCPServer } from './mcp/server';
import { logger } from './utils/logger';
import { getConfig } from './utils/config';

export async function startHTTPServer(): Promise<void> {
  const config = getConfig();
  const app = express();
  
  // Middleware
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));
  
  // CORS middleware
  app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    
    if (req.method === 'OPTIONS') {
      res.sendStatus(200);
    } else {
      next();
    }
  });

  // Health check endpoint
  app.get('/health', async (req, res) => {
    try {
      const server = new ClinikoMCPServer();
      const isConnected = await server.testConnection();
      
      res.json({
        status: isConnected ? 'healthy' : 'unhealthy',
        service: 'cliniko-mcp',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        cliniko_api: isConnected ? 'connected' : 'disconnected',
      });
    } catch (error) {
      res.status(500).json({
        status: 'unhealthy',
        service: 'cliniko-mcp',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // MCP endpoint
  app.post('/mcp', async (req, res) => {
    try {
      // Create a new MCP server instance for each request
      const mcpServer = new ClinikoMCPServer();
      
      // Handle MCP request
      const { method, params } = req.body;
      
      let result;
      switch (method) {
        case 'initialize':
          result = {
            protocolVersion: '2024-11-05',
            capabilities: { tools: {} },
            serverInfo: { name: 'cliniko-mcp', version: '1.0.0' },
          };
          break;
          
        case 'tools/list':
          // Get available tools
          result = { tools: mcpServer['getAvailableTools']() };
          break;
          
        case 'tools/call':
          // Execute tool
          result = await mcpServer['executeTool'](params.name, params.arguments);
          break;
          
        default:
          throw new Error(`Unknown method: ${method}`);
      }
      
      res.json({
        jsonrpc: '2.0',
        id: req.body.id,
        result,
      });
    } catch (error) {
      logger.error('HTTP MCP request error:', error);
      
      res.status(500).json({
        jsonrpc: '2.0',
        id: req.body.id,
        error: {
          code: -32603,
          message: error instanceof Error ? error.message : 'Internal error',
        },
      });
    }
  });

  // API documentation endpoint
  app.get('/docs', (req, res) => {
    res.json({
      service: 'Cliniko MCP Server',
      version: '1.0.0',
      description: 'Model Context Protocol server for Cliniko practice management system',
      endpoints: {
        '/health': 'GET - Health check and API connection status',
        '/mcp': 'POST - MCP protocol endpoint',
        '/docs': 'GET - This documentation',
        '/tools': 'GET - List all available tools',
      },
      mcp_tools: {
        patient_management: [
          'cliniko_list_patients',
          'cliniko_get_patient',
          'cliniko_search_patients',
          'cliniko_create_patient',
          'cliniko_update_patient',
          'cliniko_archive_patient',
          'cliniko_unarchive_patient',
          'cliniko_get_patient_medical_alerts',
          'cliniko_create_medical_alert',
          'cliniko_get_patient_attachments',
        ],
        appointment_management: [
          'cliniko_list_individual_appointments',
          'cliniko_get_individual_appointment',
          'cliniko_create_individual_appointment',
          'cliniko_update_individual_appointment',
          'cliniko_cancel_individual_appointment',
          'cliniko_check_appointment_conflicts',
          'cliniko_list_group_appointments',
          'cliniko_create_group_appointment',
          'cliniko_list_attendees',
          'cliniko_create_attendee',
          'cliniko_get_available_times',
          'cliniko_get_next_available_time',
        ],
      },
      configuration: {
        required_env_vars: [
          'CLINIKO_API_KEY',
        ],
        optional_env_vars: [
          'CLINIKO_SHARD',
          'CLINIKO_AUTO_DETECT_SHARD',
          'LOG_LEVEL',
          'CLINIKO_RATE_LIMIT',
          'CLINIKO_REQUEST_TIMEOUT',
        ],
      },
      getting_started: {
        api_key: 'Get your API key from Cliniko Settings > API Keys',
        shard_detection: 'Shard is auto-detected from API key or can be set manually',
        rate_limits: 'Cliniko allows 200 requests per minute by default',
      },
    });
  });

  // List tools endpoint
  app.get('/tools', async (req, res) => {
    try {
      const server = new ClinikoMCPServer();
      const tools = server['getAvailableTools']();
      
      res.json({
        tools: tools.map(tool => ({
          name: tool.name,
          description: tool.description,
          category: tool.name.includes('patient') ? 'patient_management' :
                   tool.name.includes('appointment') ? 'appointment_management' :
                   tool.name.includes('practitioner') ? 'practitioner_management' :
                   tool.name.includes('invoice') ? 'billing_management' :
                   'other',
        })),
        total_tools: tools.length,
        categories: {
          patient_management: tools.filter(t => t.name.includes('patient')).length,
          appointment_management: tools.filter(t => t.name.includes('appointment')).length,
        },
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to load tools',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // Error handling middleware
  app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
    logger.error('Express error:', error);
    
    res.status(500).json({
      error: 'Internal server error',
      message: error.message,
      timestamp: new Date().toISOString(),
    });
  });

  // 404 handler
  app.use((req, res) => {
    res.status(404).json({
      error: 'Not found',
      message: `Endpoint ${req.method} ${req.path} not found`,
      available_endpoints: ['/health', '/mcp', '/docs', '/tools'],
    });
  });

  // Start server
  const server = app.listen(config.httpPort, config.httpHost, () => {
    logger.info(`Cliniko MCP HTTP Server listening on http://${config.httpHost}:${config.httpPort}`);
    console.log(`Cliniko MCP HTTP Server started on http://${config.httpHost}:${config.httpPort}`);
    console.log(`Health check: http://${config.httpHost}:${config.httpPort}/health`);
    console.log(`Documentation: http://${config.httpHost}:${config.httpPort}/docs`);
    console.log(`Tools list: http://${config.httpHost}:${config.httpPort}/tools`);
  });

  // Graceful shutdown
  const shutdown = async () => {
    logger.info('Shutting down HTTP server...');
    server.close(() => {
      logger.info('HTTP server closed');
      process.exit(0);
    });
  };

  process.on('SIGTERM', shutdown);
  process.on('SIGINT', shutdown);
}
