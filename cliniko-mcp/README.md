# Cliniko MCP

A comprehensive Model Context Protocol (MCP) server that provides AI assistants and Lang<PERSON> with complete access to the Cliniko practice management system. This enables AI agents to programmatically manage patients, appointments, practitioners, invoices, and all other Cliniko functionality.

## 🏥 Overview

Cliniko MCP serves as a bridge between Cliniko's practice management platform and AI models, enabling them to understand and work with healthcare practice data effectively. It provides structured access to:

- 👥 **Patient Management** - Complete patient lifecycle from registration to medical records
- 📅 **Appointment Scheduling** - Individual and group appointments with conflict detection
- 👨‍⚕️ **Practitioner Management** - Staff scheduling, availability, and business management
- 💰 **Billing & Invoicing** - Financial management, products, and payment processing
- 📋 **Clinical Documentation** - Treatment notes, forms, and medical alerts
- ⚙️ **Reference Data** - Settings, types, and configuration management

## ✨ Features

- **🔐 Secure Authentication**: API key-based authentication with automatic shard detection
- **⚡ Rate Limiting**: Built-in rate limiting (200 requests/minute) with retry logic
- **🛡️ Error Resilience**: Comprehensive error handling and validation
- **📊 Real-time Data**: Direct integration with Cliniko's live API
- **🌐 Universal Access**: Works with any Cliniko instance across all shards
- **🔍 Advanced Search**: Powerful patient and appointment search capabilities
- **✅ Conflict Detection**: Automatic scheduling conflict detection and resolution
- **📱 Multi-format Support**: Supports both stdio (Langflow/Claude) and HTTP modes

## 🚀 Quick Start

### Option 1: npx (Fastest - No Installation!)

```bash
# Run directly with npx (no installation needed!)
npx cliniko-mcp
```

### Option 2: Local Installation

```bash
# Clone and setup
git clone https://github.com/your-org/cliniko-mcp.git
cd cliniko-mcp
npm install
npm run build

# Test it works
npm start
```

## ⚙️ Configuration

### Required Environment Variables

```bash
# Your Cliniko API key (required)
CLINIKO_API_KEY=your-cliniko-api-key-here

# Optional: Your Cliniko shard (auto-detected if not provided)
CLINIKO_SHARD=au1  # au1, au2, us1, etc.
```

### Get Your Cliniko API Key

1. Log into your Cliniko account
2. Go to **Settings** → **API Keys**
3. Click **Generate new API key**
4. Copy the key and add it to your environment

### Langflow Integration

Add to your Langflow MCP configuration:

```json
{
  "mcpServers": {
    "cliniko-mcp": {
      "command": "npx",
      "args": ["cliniko-mcp"],
      "env": {
        "CLINIKO_MCP_MODE": "stdio",
        "CLINIKO_API_KEY": "your-api-key-here",
        "LOG_LEVEL": "error",
        "DISABLE_CONSOLE_OUTPUT": "true"
      }
    }
  }
}
```

### Claude Desktop Integration

Add to `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "cliniko-mcp": {
      "command": "npx",
      "args": ["cliniko-mcp"],
      "env": {
        "CLINIKO_MCP_MODE": "stdio",
        "CLINIKO_API_KEY": "your-api-key-here",
        "LOG_LEVEL": "error",
        "DISABLE_CONSOLE_OUTPUT": "true"
      }
    }
  }
}
```

## 🛠️ Available MCP Tools

### Patient Management (10 tools)
- `cliniko_list_patients` - List patients with advanced filtering
- `cliniko_get_patient` - Get detailed patient information
- `cliniko_search_patients` - Search patients by multiple criteria
- `cliniko_create_patient` - Create new patient records
- `cliniko_update_patient` - Update existing patient information
- `cliniko_archive_patient` - Archive patients (reversible)
- `cliniko_unarchive_patient` - Restore archived patients
- `cliniko_get_patient_medical_alerts` - Get patient medical alerts
- `cliniko_create_medical_alert` - Create medical alerts for patients
- `cliniko_get_patient_attachments` - Get patient file attachments

### Appointment Management (12 tools)
- `cliniko_list_individual_appointments` - List individual appointments
- `cliniko_get_individual_appointment` - Get appointment details
- `cliniko_create_individual_appointment` - Create new appointments
- `cliniko_update_individual_appointment` - Update existing appointments
- `cliniko_cancel_individual_appointment` - Cancel appointments
- `cliniko_check_appointment_conflicts` - Check for scheduling conflicts
- `cliniko_list_group_appointments` - List group appointments
- `cliniko_create_group_appointment` - Create group appointments
- `cliniko_list_attendees` - List group appointment attendees
- `cliniko_create_attendee` - Add attendees to group appointments
- `cliniko_get_available_times` - Find available appointment slots
- `cliniko_get_next_available_time` - Get next available appointment

### Coming Soon
- **Practitioner Management** - Staff, availability, and business management
- **Billing & Invoicing** - Financial management and payment processing
- **Clinical Documentation** - Treatment notes and forms
- **Reference Data** - Settings and configuration management

## 📖 Usage Examples

### Patient Management

```javascript
// Search for patients
cliniko_search_patients({
  query: "john smith",
  limit: 10
})

// Create a new patient
cliniko_create_patient({
  first_name: "John",
  last_name: "Smith",
  email: "<EMAIL>",
  phone_number: "+61 2 1234 5678",
  date_of_birth: "1985-06-15",
  address_1: "123 Main Street",
  city: "Sydney",
  state: "NSW",
  post_code: "2000",
  country: "Australia"
})

// Update patient information
cliniko_update_patient({
  patient_id: 12345,
  email: "<EMAIL>",
  mobile_number: "+**************"
})
```

### Appointment Management

```javascript
// Check available appointment times
cliniko_get_available_times({
  business_id: 1,
  practitioner_id: 5,
  appointment_type_id: 3,
  from: "2024-01-15",
  to: "2024-01-19"
})

// Create an appointment
cliniko_create_individual_appointment({
  appointment_start: "2024-01-15T10:00:00Z",
  appointment_end: "2024-01-15T11:00:00Z",
  patient_id: 12345,
  practitioner_id: 5,
  appointment_type_id: 3,
  business_id: 1,
  notes: "Initial consultation"
})

// Check for conflicts before scheduling
cliniko_check_appointment_conflicts({
  appointment_start: "2024-01-15T10:00:00Z",
  appointment_end: "2024-01-15T11:00:00Z",
  practitioner_id: 5,
  business_id: 1
})
```

### Medical Alerts

```javascript
// Create a medical alert
cliniko_create_medical_alert({
  patient_id: 12345,
  name: "Penicillin Allergy",
  description: "Severe allergic reaction to penicillin",
  alert_type: "Allergy"
})

// Get all medical alerts for a patient
cliniko_get_patient_medical_alerts({
  patient_id: 12345
})
```

## 🔧 Advanced Configuration

### HTTP Mode (Remote Access)

```bash
# Start HTTP server
CLINIKO_MCP_MODE=http npm start
```

The server will start on `http://localhost:3000` with endpoints:
- `/health` - Health check and API connection status
- `/mcp` - MCP protocol endpoint  
- `/docs` - API documentation
- `/tools` - List all available tools

### Rate Limiting

```bash
# Custom rate limiting (default: 200 requests/minute)
CLINIKO_RATE_LIMIT=150
CLINIKO_RATE_LIMIT_WINDOW=60000  # 1 minute in milliseconds
```

### Request Configuration

```bash
# Timeout and retry settings
CLINIKO_REQUEST_TIMEOUT=30000    # 30 seconds
CLINIKO_RETRY_ATTEMPTS=3         # Number of retries
CLINIKO_RETRY_DELAY=1000         # 1 second between retries
```

### Logging

```bash
# Logging configuration
LOG_LEVEL=info                   # error, warn, info, debug
CLINIKO_MCP_LOG_PATH=./logs/cliniko-mcp.log
```

## 🏗️ Architecture

```
cliniko-mcp/
├── src/
│   ├── mcp/                 # MCP server implementation
│   │   ├── server.ts        # Main MCP server
│   │   ├── tools/           # MCP tool definitions
│   │   └── handlers/        # Tool execution handlers
│   ├── services/            # Core services
│   │   ├── cliniko_api.ts   # Cliniko API client
│   │   └── auth.ts          # Authentication handling
│   ├── models/              # TypeScript data models
│   └── utils/               # Configuration and utilities
├── docs/                    # Documentation
└── examples/                # Usage examples
```

## 🧪 Development

```bash
# Install dependencies
npm install

# Build TypeScript
npm run build

# Run in development mode
npm run dev

# Run in HTTP mode
npm run dev:http

# Run tests
npm test

# Type checking
npm run typecheck
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Run tests (`npm test`)
4. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) for details.

## 🙏 Acknowledgments

- [Cliniko](https://cliniko.com) team for the excellent practice management platform
- [Anthropic](https://anthropic.com) for the Model Context Protocol
- Healthcare professionals who inspire better practice management tools

---

**Built with ❤️ for healthcare professionals**  
Making AI + Cliniko practice management delightful
