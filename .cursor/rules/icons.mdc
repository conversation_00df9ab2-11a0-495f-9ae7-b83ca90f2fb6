---
description: "Rules for creating and implementing component icons in Langflow, covering both backend Python component icon attributes and frontend React icon implementation."
globs:
  - "src/frontend/src/icons/**/*"
  - "src/frontend/src/icons/lazyIconImports.ts"
  - "src/backend/**/*component*.py"
  - "src/backend/**/components/**/*.py"
alwaysApply: false
---


# Component Icon Rules

## Purpose
To ensure consistent, clear, and functional icon usage for components, covering both backend (Python) and frontend (React/TypeScript) steps.

---

## 1. Backend (Python) — Setting the Icon Name

- **Where:** In your component class (e.g., in `src/backend/base/langflow/components/vectorstores/astradb.py`)
- **How:**
  Set the `icon` attribute to a string matching the icon you want to use.
  ```python
  icon = "AstraDB"
  ```
- **Tip:**
  The string must match the frontend icon mapping exactly (case-sensitive).

---

## 2. Frontend (React/TypeScript) — Adding the Icon

### a. Create the Icon Component

- **Where:**
  In a new directory for your icon, e.g., `src/frontend/src/icons/AstraDB/`.
- **How:**
  - Add your SVG as a React component, e.g., `AstraSVG` in `AstraDB.jsx`.
    ```jsx
    const AstraSVG = (props) => (
      <svg {...props}>
        <path
          // ...
        />
      </svg>
    );
    ```
  - Create an `index.tsx` that exports your icon using `forwardRef`:
    ```tsx
    import React, { forwardRef } from "react";
    import AstraSVG from "./AstraDB";

    export const AstraDBIcon = forwardRef<
      SVGSVGElement,
      React.PropsWithChildren<{}>
    >((props, ref) => {
      return <AstraSVG ref={ref} isDark={isDark} {...props} />;
    });
    ```

#### Supporting Light and Dark Mode Icons

- **How:**
  - In your SVG component (e.g., `AstraDB.jsx`), use the `isDark` prop to switch colors:
    ```jsx
    const AstraSVG = (props) => (
      <svg {...props}>
        <path
          fill={props.isDark ? "#ffffff" : "#0A0A0A"}
          // ...
        />
      </svg>
    );
    ```
  - The `isDark` prop is passed from the icon wrapper (see above) and should be used to toggle between light and dark color schemes.
  - You can use a utility like `stringToBool` to ensure the prop is interpreted correctly.

### b. Add to Lazy Icon Imports

- **Where:**
  In `src/frontend/src/icons/lazyIconImports.ts`
- **How:**
  Add an entry to the `lazyIconsMapping` object:
  ```ts
  AstraDB: () =>
    import("@/icons/AstraDB").then((mod) => ({ default: mod.AstraDBIcon })),
  ```
- **Tip:**
  The key (`AstraDB`) must match the string used in the backend.

---

## 3. Best Practices

- **Naming:**
  Use clear, recognizable names (e.g., `"AstraDB"`, `"Postgres"`, `"OpenAI"`).
- **Consistency:**
  Always use the same icon name for the same service across backend and frontend.
- **Missing Icon:**
  If no icon exists, use a [lucide icon](https://lucide.dev/icons)
- **Light/Dark Mode:**
  Always support both light and dark mode for custom icons by using the `isDark` prop in your SVG.

---

## 4. Checklist for Adding a New Icon

- [ ] Decide on a clear, descriptive icon name (e.g., `AstraDB`).
- [ ] In your Python component, set `icon = "YourIconName"`.
- [ ] Create a new icon directory in `src/frontend/src/icons/YourIconName/`.
- [ ] Add your SVG as a React component (e.g., `YourIconNameIcon.jsx`).
- [ ] Create an `index.tsx` that exports your icon using `forwardRef` and passes the `isDark` prop.
- [ ] Add your icon to `lazyIconsMapping` in `src/frontend/src/icons/lazyIconImports.ts` with the exact same name.
- [ ] Verify the icon appears correctly in the UI in both light and dark mode.
- [ ] If no suitable icon exists, use a generic icon and request a new one if needed.

---

**Example for AstraDB:**
- Backend:
  ```python
  icon = "AstraDB"
  ```
- Frontend:
  - `src/icons/AstraDB/AstraDB.jsx` (SVG as React component, uses `isDark` prop)
  - `src/icons/AstraDB/index.tsx` (exports `AstraDBIcon` and passes `isDark`)
  - Add to `lazyIconImports.ts`:
    ```ts
    AstraDB: () =>
      import("@/icons/AstraDB").then((mod) => ({ default: mod.AstraDBIcon })),
    ```

---
