{"name": "cdk", "version": "0.1.0", "bin": {"cdk": "bin/cdk.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^20.12.12", "aws-cdk": "^2.1000.2", "jest": "^29.7.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.4.5"}, "dependencies": {"@aws-solutions-constructs/aws-cloudfront-s3": "^2.57.0", "aws-cdk-lib": "^2.141.0", "cdk-ecr-deployment": "^3.0.55", "constructs": "^10.3.0", "deploy-time-build": "^0.3.21", "dotenv": "^16.4.5", "source-map-support": "^0.5.21"}}