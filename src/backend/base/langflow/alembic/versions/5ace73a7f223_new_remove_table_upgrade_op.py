"""new remove table upgrade op

Revision ID: 5ace73a7f223
Revises: 0ae3a2674f32
Create Date: 2024-10-08 10:59:12.980671

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.engine.reflection import Inspector
from langflow.utils import migration
from sqlalchemy.dialects import sqlite
from langflow.utils import migration

# revision identifiers, used by Alembic.
revision: str = "5ace73a7f223"
down_revision: Union[str, None] = "0ae3a2674f32"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:

    with op.batch_alter_table("message", schema=None) as batch_op:
        batch_op.alter_column("text", existing_type=sa.TEXT(), nullable=True)

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("message", schema=None) as batch_op:
        batch_op.alter_column("text", existing_type=sa.TEXT(), nullable=False)
    
    # ### end Alembic commands ###
