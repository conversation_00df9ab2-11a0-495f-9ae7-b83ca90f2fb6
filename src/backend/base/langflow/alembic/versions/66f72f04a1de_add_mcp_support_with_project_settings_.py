"""Add MCP support with project settings in flows

Revision ID: 66f72f04a1de
Revises: e56d87f8994a
Create Date: 2025-04-24 18:42:15.828332

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.engine.reflection import Inspector
from langflow.utils import migration


# revision identifiers, used by Alembic.
revision: str = '66f72f04a1de'
down_revision: Union[str, None] = 'e56d87f8994a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    conn = op.get_bind()
    inspector = sa.inspect(conn)  # type: ignore
    column_names = [column["name"] for column in inspector.get_columns("flow")]
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('flow', schema=None) as batch_op:
        if 'mcp_enabled' not in column_names:
            batch_op.add_column(sa.Column('mcp_enabled', sa.<PERSON><PERSON>(), nullable=True))
        if 'action_name' not in column_names:
            batch_op.add_column(sa.Column('action_name', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
        if 'action_description' not in column_names:
            batch_op.add_column(sa.Column('action_description', sa.Text(), nullable=True))

    # ### end Alembic commands ###


def downgrade() -> None:
    conn = op.get_bind()
    inspector = sa.inspect(conn)  # type: ignore
    column_names = [column["name"] for column in inspector.get_columns("flow")]

    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('flow', schema=None) as batch_op:
        if 'action_description' in column_names:
            batch_op.drop_column('action_description')
        if 'action_name' in column_names:
            batch_op.drop_column('action_name')
        if 'mcp_enabled' in column_names:
            batch_op.drop_column('mcp_enabled')

    # ### end Alembic commands ###
