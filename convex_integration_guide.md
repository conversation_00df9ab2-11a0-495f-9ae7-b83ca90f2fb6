# Convex + Pica OS Integration for Langflow

## 🎯 **Overview**

This guide shows how to create custom Langflow components that integrate Convex databases using Pica OS MCP tools. The integration provides full access to Convex functionality through Langflow's visual interface.

## 📁 **Installation**

### 1. **Add Components to Langflow**

Place the component files in your Langflow installation:

```
src/backend/base/langflow/components/convex/
├── __init__.py
├── convex_pica.py          # Basic Convex operations
└── convex_schema.py        # Schema management
```

### 2. **Update Component Registry**

Add to `src/backend/base/langflow/components/__init__.py`:

```python
from .convex import ConvexPicaComponent, ConvexSchemaComponent
```

### 3. **Install Dependencies**

Add to `pyproject.toml`:

```toml
[tool.poetry.dependencies]
httpx = "^0.25.0"
loguru = "^0.7.0"
```

## 🔧 **Component Features**

### **ConvexPicaComponent**
- ✅ Execute Convex queries, mutations, and actions
- ✅ Function path validation and argument parsing
- ✅ Comprehensive error handling and logging
- ✅ Raw response access for debugging

### **ConvexSchemaComponent**
- ✅ List database schemas and table structures
- ✅ Manage table data (clear, snapshot, deltas)
- ✅ Index management and optimization
- ✅ Advanced metadata handling

## 🚀 **Usage Examples**

### **Basic Query Example**

```python
# Component Configuration
{
    "pica_api_key": "your-pica-api-key",
    "pica_connection_key": "live::convex::default::xxxxx",
    "operation_type": "query",
    "function_path": "messages/list",
    "function_args": '{"limit": 10, "channel": "general"}'
}
```

### **Mutation Example**

```python
# Component Configuration
{
    "operation_type": "mutation",
    "function_path": "users/create",
    "function_args": '{"name": "John Doe", "email": "<EMAIL>"}'
}
```

### **Schema Management Example**

```python
# Schema Component Configuration
{
    "schema_operation": "list_schemas",
    "include_metadata": true,
    "delta_schema": false
}
```

## 🔗 **Integration Patterns**

### **1. Data Pipeline Pattern**

```
[Text Input] → [Convex Query] → [Data Processing] → [Convex Mutation] → [Output]
```

### **2. Schema Discovery Pattern**

```
[Schema Manager] → [Table Info] → [Dynamic Query Builder] → [Convex Query]
```

### **3. Real-time Updates Pattern**

```
[Webhook] → [Convex Mutation] → [Document Deltas] → [Notification]
```

## 🛠 **Advanced Configuration**

### **Pica Connection Setup**

1. **Get Pica API Key**: Sign up at Pica OS and generate an API key
2. **Create Convex Connection**: Connect your Convex deployment to Pica
3. **Get Connection Key**: Copy the connection key (format: `live::convex::default::xxxxx`)

### **Error Handling**

The components include comprehensive error handling:

- ✅ **Input Validation**: Validates all inputs before execution
- ✅ **Network Errors**: Handles timeouts and connection issues
- ✅ **API Errors**: Processes Convex and Pica API error responses
- ✅ **JSON Parsing**: Validates and parses function arguments

### **Logging and Debugging**

- **Execution Logs**: Include Convex function execution logs
- **Request Tracing**: Log all Pica API requests and responses
- **Error Details**: Detailed error messages with context

## 📊 **Response Formats**

### **Successful Query Response**

```json
{
    "success": true,
    "value": [
        {"_id": "msg1", "text": "Hello", "author": "user1"},
        {"_id": "msg2", "text": "World", "author": "user2"}
    ],
    "operation": "query",
    "function_path": "messages/list",
    "logs": ["Query executed successfully"]
}
```

### **Error Response**

```json
{
    "success": false,
    "error": "Function not found: messages/invalid",
    "operation": "query",
    "function_path": "messages/invalid",
    "error_details": {"code": "FUNCTION_NOT_FOUND"}
}
```

### **Schema Response**

```json
{
    "operation": "list_schemas",
    "success": true,
    "data": {
        "messages": {
            "type": "object",
            "properties": {
                "_id": {"type": "string"},
                "text": {"type": "string"},
                "author": {"type": "string"},
                "timestamp": {"type": "number"}
            }
        }
    }
}
```

## 🔒 **Security Best Practices**

1. **API Key Management**: Store Pica API keys securely using Langflow's SecretStrInput
2. **Connection Validation**: Validate connection keys before use
3. **Input Sanitization**: Sanitize all user inputs and function arguments
4. **Error Disclosure**: Avoid exposing sensitive information in error messages

## 🧪 **Testing**

### **Unit Tests**

```python
import pytest
from langflow.components.convex import ConvexPicaComponent

@pytest.mark.asyncio
async def test_convex_query():
    component = ConvexPicaComponent(
        pica_api_key="test-key",
        pica_connection_key="live::convex::default::test",
        operation_type="query",
        function_path="messages/list",
        function_args="{}"
    )
    
    # Mock the Pica API response
    # ... test implementation
```

### **Integration Tests**

Test with actual Convex deployment and Pica connection to ensure end-to-end functionality.

## 📈 **Performance Optimization**

1. **Connection Pooling**: Use httpx.AsyncClient for efficient HTTP connections
2. **Request Timeouts**: Configure appropriate timeouts for different operations
3. **Batch Operations**: Use batch operations for multiple related queries
4. **Caching**: Cache schema information to reduce API calls

## 🔄 **Future Enhancements**

- **Real-time Subscriptions**: Add support for Convex subscriptions
- **Batch Operations**: Support for multiple operations in a single request
- **Schema Validation**: Validate function arguments against schemas
- **Performance Metrics**: Add execution time and performance tracking
- **Visual Query Builder**: GUI for building complex queries

## 📚 **Resources**

- [Convex Documentation](https://docs.convex.dev/)
- [Pica OS Documentation](https://docs.pica.ai/)
- [Langflow Custom Components](https://docs.langflow.org/components-custom-components)
- [Component Examples](https://github.com/langflow-ai/langflow/tree/main/src/backend/base/langflow/components)
