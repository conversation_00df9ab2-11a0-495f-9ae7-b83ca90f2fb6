import { LangflowMCPServer } from '../src/mcp/server';
import { getConfig } from '../src/utils/config';

describe('LangflowMCPServer', () => {
  let server: LangflowMCPServer;

  beforeEach(() => {
    // Mock environment variables for testing
    process.env.LANGFLOW_MCP_MODE = 'stdio';
    process.env.LOG_LEVEL = 'error';
    process.env.DISABLE_CONSOLE_OUTPUT = 'true';
  });

  afterEach(() => {
    // Clean up environment variables
    delete process.env.LANGFLOW_API_URL;
    delete process.env.LANGFLOW_API_KEY;
  });

  describe('initialization', () => {
    it('should initialize without Langflow API configuration', () => {
      expect(() => {
        server = new LangflowMCPServer();
      }).not.toThrow();
    });

    it('should initialize with Langflow API configuration', () => {
      process.env.LANGFLOW_API_URL = 'http://localhost:7860';
      process.env.LANGFLOW_API_KEY = 'test-key';

      expect(() => {
        server = new LangflowMCPServer();
      }).not.toThrow();
    });

    it('should throw error with invalid API configuration', () => {
      process.env.LANGFLOW_API_URL = 'invalid-url';
      process.env.LANGFLOW_API_KEY = 'test-key';

      expect(() => {
        server = new LangflowMCPServer();
      }).toThrow();
    });
  });

  describe('configuration', () => {
    it('should load default configuration', () => {
      const config = getConfig();
      
      expect(config.mode).toBe('stdio');
      expect(config.logLevel).toBe('error');
      expect(config.disableConsoleOutput).toBe(true);
      expect(config.componentCacheTTL).toBe(3600);
      expect(config.defaultSessionTimeout).toBe(300);
      expect(config.maxConcurrentExecutions).toBe(5);
    });

    it('should override configuration with environment variables', () => {
      process.env.COMPONENT_CACHE_TTL = '7200';
      process.env.DEFAULT_SESSION_TIMEOUT = '600';
      process.env.MAX_CONCURRENT_EXECUTIONS = '10';

      const config = getConfig();
      
      expect(config.componentCacheTTL).toBe(7200);
      expect(config.defaultSessionTimeout).toBe(600);
      expect(config.maxConcurrentExecutions).toBe(10);
    });
  });

  describe('tool availability', () => {
    it('should provide component discovery tools without API', () => {
      server = new LangflowMCPServer();
      const tools = server['getAvailableTools']();
      
      const componentTools = tools.filter(tool => 
        tool.name.includes('component') || 
        tool.name.includes('search_components') ||
        tool.name.includes('list_components')
      );
      
      expect(componentTools.length).toBeGreaterThan(0);
    });

    it('should provide all tools with API configuration', () => {
      process.env.LANGFLOW_API_URL = 'http://localhost:7860';
      process.env.LANGFLOW_API_KEY = 'test-key';

      server = new LangflowMCPServer();
      const tools = server['getAvailableTools']();
      
      const flowTools = tools.filter(tool => tool.name.includes('flow'));
      const executionTools = tools.filter(tool => 
        tool.name.includes('run_') || tool.name.includes('execution')
      );
      
      expect(flowTools.length).toBeGreaterThan(0);
      expect(executionTools.length).toBeGreaterThan(0);
    });
  });

  describe('error handling', () => {
    it('should handle unknown tool gracefully', async () => {
      server = new LangflowMCPServer();
      
      await expect(
        server['executeTool']('unknown_tool', {})
      ).rejects.toThrow('Unknown tool: unknown_tool');
    });

    it('should handle API errors gracefully', async () => {
      process.env.LANGFLOW_API_URL = 'http://localhost:7860';
      process.env.LANGFLOW_API_KEY = 'invalid-key';

      server = new LangflowMCPServer();
      
      await expect(
        server['executeTool']('langflow_list_flows', {})
      ).rejects.toThrow();
    });
  });
});
