# Basic Flow Creation Example

This example demonstrates how to create a simple chat flow using Langflow MCP tools.

## Overview

We'll create a basic flow that:
1. Takes user input via ChatInput component
2. Processes it with an OpenAI component
3. Returns the response via ChatOutput component

## Step-by-Step Guide

### 1. Discover Available Components

First, let's find the components we need:

```javascript
// Search for input components
langflow_search_components({
  query: "chat input",
  limit: 5
})

// Search for OpenAI components
langflow_search_components({
  query: "openai",
  category: "llms",
  limit: 5
})

// Search for output components
langflow_search_components({
  query: "chat output",
  limit: 5
})
```

### 2. Get Component Details

Get detailed information about each component:

```javascript
// Get ChatInput details
langflow_get_component_details({
  component_name: "ChatInput",
  include_template: true,
  include_examples: true
})

// Get OpenAI details
langflow_get_component_details({
  component_name: "OpenAIModel",
  category: "llms",
  include_template: true
})

// Get ChatOutput details
langflow_get_component_details({
  component_name: "ChatOutput",
  include_template: true
})
```

### 3. Validate Component Configurations

Before creating the flow, validate our component configurations:

```javascript
// Validate ChatInput configuration
langflow_validate_component_config({
  component_name: "ChatInput",
  config: {
    input_value: "",
    sender: "User",
    sender_name: "User",
    session_id: "",
    should_store_message: true
  }
})

// Validate OpenAI configuration
langflow_validate_component_config({
  component_name: "OpenAIModel",
  config: {
    api_key: "your-openai-api-key",
    model_name: "gpt-3.5-turbo",
    temperature: 0.7,
    max_tokens: 1000
  }
})

// Validate ChatOutput configuration
langflow_validate_component_config({
  component_name: "ChatOutput",
  config: {
    data_template: "{text}",
    should_store_message: true
  }
})
```

### 4. Create the Flow

Now create the complete flow:

```javascript
langflow_create_flow({
  name: "Basic Chat Flow",
  description: "A simple chat flow using OpenAI",
  data: {
    nodes: [
      {
        id: "chatinput-1",
        type: "ChatInput",
        position: [100, 200],
        data: {
          type: "ChatInput",
          node: {
            template: {
              input_value: {
                type: "str",
                required: false,
                placeholder: "",
                list: false,
                show: true,
                multiline: true,
                value: "",
                password: false,
                name: "input_value",
                display_name: "Text",
                advanced: false,
                input_types: ["Message"],
                dynamic: false,
                info: "",
                title_case: false
              },
              sender: {
                type: "str",
                required: false,
                placeholder: "",
                list: false,
                show: true,
                multiline: false,
                value: "User",
                password: false,
                name: "sender",
                display_name: "Sender Type",
                advanced: false,
                options: ["Machine", "User"],
                dynamic: false,
                info: "",
                title_case: false
              },
              sender_name: {
                type: "str",
                required: false,
                placeholder: "",
                list: false,
                show: true,
                multiline: false,
                value: "User",
                password: false,
                name: "sender_name",
                display_name: "Sender Name",
                advanced: false,
                dynamic: false,
                info: "",
                title_case: false
              },
              session_id: {
                type: "str",
                required: false,
                placeholder: "",
                list: false,
                show: true,
                multiline: false,
                value: "",
                password: false,
                name: "session_id",
                display_name: "Session ID",
                advanced: true,
                dynamic: false,
                info: "",
                title_case: false
              },
              should_store_message: {
                type: "bool",
                required: false,
                placeholder: "",
                list: false,
                show: true,
                multiline: false,
                value: true,
                password: false,
                name: "should_store_message",
                display_name: "Store Messages",
                advanced: true,
                dynamic: false,
                info: "",
                title_case: false
              }
            },
            description: "Get chat inputs from the Playground.",
            base_classes: ["Message"],
            name: "ChatInput",
            display_name: "Chat Input",
            documentation: "",
            custom_fields: {},
            output_types: ["Message"],
            field_formatters: {},
            beta: false,
            error: null,
            flow: false
          },
          id: "chatinput-1"
        }
      },
      {
        id: "openaimodel-1",
        type: "OpenAIModel",
        position: [400, 200],
        data: {
          type: "OpenAIModel",
          node: {
            template: {
              api_key: {
                type: "str",
                required: false,
                placeholder: "",
                list: false,
                show: true,
                multiline: false,
                value: "",
                password: true,
                name: "api_key",
                display_name: "OpenAI API Key",
                advanced: false,
                dynamic: false,
                info: "",
                title_case: false
              },
              input_value: {
                type: "str",
                required: true,
                placeholder: "",
                list: false,
                show: true,
                multiline: true,
                value: "",
                password: false,
                name: "input_value",
                display_name: "Input",
                advanced: false,
                input_types: ["Message"],
                dynamic: false,
                info: "",
                title_case: false
              },
              model_name: {
                type: "str",
                required: false,
                placeholder: "",
                list: true,
                show: true,
                multiline: false,
                value: "gpt-3.5-turbo",
                password: false,
                name: "model_name",
                display_name: "Model Name",
                advanced: false,
                options: ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"],
                dynamic: false,
                info: "",
                title_case: false
              },
              temperature: {
                type: "float",
                required: false,
                placeholder: "",
                list: false,
                show: true,
                multiline: false,
                value: 0.7,
                password: false,
                name: "temperature",
                display_name: "Temperature",
                advanced: false,
                dynamic: false,
                info: "",
                title_case: false
              },
              max_tokens: {
                type: "int",
                required: false,
                placeholder: "",
                list: false,
                show: true,
                multiline: false,
                value: 1000,
                password: false,
                name: "max_tokens",
                display_name: "Max Tokens",
                advanced: true,
                dynamic: false,
                info: "",
                title_case: false
              }
            },
            description: "OpenAI large language models.",
            base_classes: ["BaseLanguageModel"],
            name: "OpenAIModel",
            display_name: "OpenAI",
            documentation: "",
            custom_fields: {},
            output_types: ["BaseLanguageModel"],
            field_formatters: {},
            beta: false,
            error: null,
            flow: false
          },
          id: "openaimodel-1"
        }
      },
      {
        id: "chatoutput-1",
        type: "ChatOutput",
        position: [700, 200],
        data: {
          type: "ChatOutput",
          node: {
            template: {
              input_value: {
                type: "str",
                required: true,
                placeholder: "",
                list: false,
                show: true,
                multiline: true,
                value: "",
                password: false,
                name: "input_value",
                display_name: "Text",
                advanced: false,
                input_types: ["Message"],
                dynamic: false,
                info: "",
                title_case: false
              },
              data_template: {
                type: "str",
                required: false,
                placeholder: "",
                list: false,
                show: true,
                multiline: true,
                value: "{text}",
                password: false,
                name: "data_template",
                display_name: "Data Template",
                advanced: true,
                dynamic: false,
                info: "",
                title_case: false
              },
              should_store_message: {
                type: "bool",
                required: false,
                placeholder: "",
                list: false,
                show: true,
                multiline: false,
                value: true,
                password: false,
                name: "should_store_message",
                display_name: "Store Messages",
                advanced: true,
                dynamic: false,
                info: "",
                title_case: false
              }
            },
            description: "Display a chat message in the Playground.",
            base_classes: ["Message"],
            name: "ChatOutput",
            display_name: "Chat Output",
            documentation: "",
            custom_fields: {},
            output_types: ["Message"],
            field_formatters: {},
            beta: false,
            error: null,
            flow: false
          },
          id: "chatoutput-1"
        }
      }
    ],
    edges: [
      {
        id: "edge-1",
        source: "chatinput-1",
        target: "openaimodel-1",
        sourceHandle: "chatinput-1-output-0",
        targetHandle: "openaimodel-1-input-0"
      },
      {
        id: "edge-2", 
        source: "openaimodel-1",
        target: "chatoutput-1",
        sourceHandle: "openaimodel-1-output-0",
        targetHandle: "chatoutput-1-input-0"
      }
    ],
    viewport: {
      x: 0,
      y: 0,
      zoom: 1
    }
  },
  tags: ["chat", "openai", "basic"],
  webhook: false
})
```

### 5. Validate the Flow

Validate the complete flow before testing:

```javascript
langflow_validate_flow({
  flow_data: {
    nodes: [/* nodes from above */],
    edges: [/* edges from above */]
  }
})
```

### 6. Test the Flow

Run the flow with a test input:

```javascript
langflow_run_flow({
  flow_id_or_name: "your-flow-id", // Use the ID returned from create_flow
  input_value: "Hello! Can you help me understand how Langflow works?",
  input_type: "chat",
  output_type: "chat"
})
```

## Expected Result

The flow should:
1. Accept your input message
2. Send it to OpenAI for processing
3. Return the AI's response
4. Display it in the chat output

## Next Steps

- Try modifying the OpenAI parameters (temperature, model, etc.)
- Add more components like memory or prompt templates
- Experiment with different input/output types
- Create more complex flows with multiple LLM calls

## Troubleshooting

- **API Key Issues**: Make sure your OpenAI API key is valid and has sufficient credits
- **Connection Errors**: Verify your Langflow instance is running and accessible
- **Validation Failures**: Check component configurations match the expected schema
- **Flow Execution Errors**: Review the execution logs for specific error messages
