# Installation Guide

This guide covers different ways to install and run Langflow MCP.

## Prerequisites

- [Node.js](https://nodejs.org/) (version 16 or higher)
- A running Langflow instance (for flow management features)
- Langflow API key (for flow management features)

## Installation Methods

### 1. NPX (Recommended for Quick Start)

The fastest way to get started - no installation required:

```bash
npx langflow-mcp
```

This will automatically download and run the latest version.

### 2. Global Installation

Install globally to use from anywhere:

```bash
npm install -g langflow-mcp
langflow-mcp
```

### 3. Local Development Setup

For development or customization:

```bash
# Clone the repository
git clone https://github.com/your-org/langflow-mcp.git
cd langflow-mcp

# Install dependencies
npm install

# Build the project
npm run build

# Run the server
npm start
```

## Configuration

### Environment Variables

Create a `.env` file in your project directory:

```bash
# Required for flow management
LANGFLOW_API_URL=http://localhost:7860
LANGFLOW_API_KEY=your-api-key-here

# Optional configuration
LANGFLOW_MCP_MODE=stdio          # stdio (Claude Desktop) or http (remote)
LOG_LEVEL=error                  # error, warn, info, debug
DISABLE_CONSOLE_OUTPUT=true      # Disable console output in stdio mode
COMPONENT_CACHE_TTL=3600         # Component cache TTL in seconds
DEFAULT_SESSION_TIMEOUT=300      # Default session timeout in seconds
MAX_CONCURRENT_EXECUTIONS=5      # Maximum concurrent flow executions
```

### Getting Langflow API Key

1. Open your Langflow instance (usually `http://localhost:7860`)
2. Go to Settings → API Keys
3. Create a new API key
4. Copy the key and add it to your `.env` file

## Claude Desktop Integration

### Configuration File Locations

- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
- **Linux**: `~/.config/Claude/claude_desktop_config.json`

### Basic Configuration (Component Discovery Only)

```json
{
  "mcpServers": {
    "langflow-mcp": {
      "command": "npx",
      "args": ["langflow-mcp"],
      "env": {
        "LANGFLOW_MCP_MODE": "stdio",
        "LOG_LEVEL": "error",
        "DISABLE_CONSOLE_OUTPUT": "true"
      }
    }
  }
}
```

### Full Configuration (With Flow Management)

```json
{
  "mcpServers": {
    "langflow-mcp": {
      "command": "npx",
      "args": ["langflow-mcp"],
      "env": {
        "LANGFLOW_MCP_MODE": "stdio",
        "LOG_LEVEL": "error",
        "DISABLE_CONSOLE_OUTPUT": "true",
        "LANGFLOW_API_URL": "http://localhost:7860",
        "LANGFLOW_API_KEY": "your-api-key-here"
      }
    }
  }
}
```

### Local Installation Configuration

If you installed locally:

```json
{
  "mcpServers": {
    "langflow-mcp": {
      "command": "node",
      "args": ["/absolute/path/to/langflow-mcp/dist/mcp/index.js"],
      "env": {
        "LANGFLOW_MCP_MODE": "stdio",
        "LOG_LEVEL": "error",
        "DISABLE_CONSOLE_OUTPUT": "true",
        "LANGFLOW_API_URL": "http://localhost:7860",
        "LANGFLOW_API_KEY": "your-api-key-here"
      }
    }
  }
}
```

## HTTP Mode (Remote Access)

For remote access or integration with other tools:

```bash
# Set environment variables
export LANGFLOW_MCP_MODE=http
export LANGFLOW_API_URL=http://localhost:7860
export LANGFLOW_API_KEY=your-api-key

# Start HTTP server
npm start
```

The server will start on `http://localhost:3000` by default.

### HTTP Configuration Options

```bash
# Custom port and host
export HTTP_PORT=8080
export HTTP_HOST=0.0.0.0

# Start server
npm start
```

## Verification

### Test the Installation

1. **Check if the server starts:**
   ```bash
   npx langflow-mcp
   ```

2. **Test Claude Desktop integration:**
   - Restart Claude Desktop after updating the configuration
   - Ask Claude: "What Langflow tools are available?"

3. **Test HTTP mode:**
   ```bash
   curl http://localhost:3000/health
   ```

### Troubleshooting

#### Common Issues

1. **"Langflow API connection failed"**
   - Verify Langflow is running
   - Check LANGFLOW_API_URL is correct
   - Verify API key is valid

2. **"Component cache initialization failed"**
   - Check network connectivity to Langflow
   - Verify API permissions

3. **Claude Desktop not showing tools**
   - Restart Claude Desktop after configuration changes
   - Check configuration file syntax
   - Verify file paths are absolute

#### Debug Mode

Enable debug logging:

```bash
export LOG_LEVEL=debug
export DISABLE_CONSOLE_OUTPUT=false
npm start
```

#### Log Files

Logs are written to:
- Console (if not disabled)
- File (if LANGFLOW_MCP_LOG_PATH is set)

```bash
export LANGFLOW_MCP_LOG_PATH=./logs/langflow-mcp.log
npm start
```

## Next Steps

- Read the [Usage Guide](usage.md) to learn how to use the tools
- Check out [Examples](examples/) for common workflows
- See [API Reference](api-reference.md) for detailed tool documentation
