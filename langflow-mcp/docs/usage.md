# Usage Guide

This guide covers how to use Langflow MCP tools effectively with AI assistants.

## Getting Started

Once Langflow MCP is installed and configured, you can start using it with AI assistants like <PERSON>. The tools are organized into three main categories:

1. **Component Discovery** - Explore available Langflow components
2. **Flow Management** - Create, edit, and manage flows
3. **Flow Execution** - Run flows and monitor executions

## Component Discovery

### List All Components

```javascript
langflow_list_components({
  include_custom: true,
  include_beta: false
})
```

### Search for Specific Components

```javascript
langflow_search_components({
  query: "openai",
  limit: 10,
  category: "llms"
})
```

### Get Component Details

```javascript
langflow_get_component_details({
  component_name: "OpenAIModel",
  include_examples: true,
  include_template: true
})
```

### Explore Component Categories

```javascript
langflow_get_component_categories({
  include_counts: true,
  include_subcategories: true
})
```

## Flow Management

### List Existing Flows

```javascript
langflow_list_flows({
  limit: 20,
  components_only: false
})
```

### Get Flow Details

```javascript
langflow_get_flow({
  flow_id: "your-flow-id"
})
```

### Create a New Flow

```javascript
langflow_create_flow({
  name: "My New Flow",
  description: "A simple chat flow",
  data: {
    nodes: [
      {
        id: "input-1",
        type: "ChatInput",
        position: [100, 100],
        data: {
          type: "ChatInput",
          node: {
            template: {
              input_value: {
                type: "str",
                value: "",
                show: true
              }
            }
          }
        }
      }
    ],
    edges: []
  },
  tags: ["chat", "simple"]
})
```

### Update an Existing Flow

```javascript
langflow_update_flow({
  flow_id: "your-flow-id",
  name: "Updated Flow Name",
  description: "Updated description",
  tags: ["updated", "chat"]
})
```

### Validate Flow Configuration

```javascript
langflow_validate_flow({
  flow_data: {
    nodes: [/* your nodes */],
    edges: [/* your edges */]
  }
})
```

### Duplicate a Flow

```javascript
langflow_duplicate_flow({
  flow_id: "source-flow-id",
  new_name: "Copy of Original Flow"
})
```

### Delete a Flow

```javascript
langflow_delete_flow({
  flow_id: "flow-to-delete"
})
```

## Flow Execution

### Run a Flow

```javascript
langflow_run_flow({
  flow_id_or_name: "your-flow-id",
  input_value: "Hello, how are you?",
  input_type: "chat",
  output_type: "chat",
  session_id: "optional-session-id"
})
```

### Run with Streaming

```javascript
langflow_run_flow_stream({
  flow_id_or_name: "your-flow-id",
  input_value: "Tell me a story",
  max_events: 50
})
```

### Trigger via Webhook

```javascript
langflow_trigger_webhook({
  flow_id_or_name: "webhook-flow-id",
  data: {
    message: "Hello from webhook",
    user_id: "123"
  },
  headers: {
    "Content-Type": "application/json"
  }
})
```

### Check Execution Status

```javascript
langflow_get_execution_status({
  execution_id: "exec-id",
  include_outputs: true,
  include_logs: false
})
```

### List Recent Executions

```javascript
langflow_list_executions({
  flow_id: "your-flow-id",
  status: "success",
  limit: 10
})
```

## Advanced Usage Patterns

### Building Complex Flows

1. **Start with Component Discovery**
   ```javascript
   // Find components you need
   langflow_search_components({ query: "memory" })
   langflow_search_components({ query: "prompt template" })
   ```

2. **Get Detailed Component Information**
   ```javascript
   // Understand component inputs/outputs
   langflow_get_component_details({ 
     component_name: "ConversationBufferMemory" 
   })
   ```

3. **Validate Before Creating**
   ```javascript
   // Validate your flow structure
   langflow_validate_flow({ flow_data: yourFlowData })
   ```

4. **Create and Test**
   ```javascript
   // Create the flow
   const result = langflow_create_flow({ /* flow config */ })
   
   // Test it immediately
   langflow_run_flow({
     flow_id_or_name: result.flow.id,
     input_value: "test input"
   })
   ```

### Session Management

```javascript
// Start a conversation
const session1 = langflow_run_flow({
  flow_id_or_name: "chat-flow",
  input_value: "Hi, I'm John",
  session_id: "user-123-session"
})

// Continue the conversation
const session2 = langflow_run_flow({
  flow_id_or_name: "chat-flow", 
  input_value: "What's my name?",
  session_id: "user-123-session" // Same session
})

// Clear session when done
langflow_clear_session({
  session_id: "user-123-session"
})
```

### Error Handling

```javascript
try {
  const result = langflow_run_flow({
    flow_id_or_name: "my-flow",
    input_value: "test"
  })
  console.log("Success:", result)
} catch (error) {
  console.error("Flow execution failed:", error.message)
  
  // Check execution status for more details
  if (error.execution_id) {
    const status = langflow_get_execution_status({
      execution_id: error.execution_id,
      include_logs: true
    })
    console.log("Execution details:", status)
  }
}
```

### Performance Monitoring

```javascript
// Get flow performance metrics
langflow_get_flow_metrics({
  flow_id: "your-flow-id",
  time_range: "24h",
  include_component_metrics: true
})

// Monitor recent executions
langflow_list_executions({
  flow_id: "your-flow-id",
  status: "error", // Find failed executions
  limit: 20
})
```

## Best Practices

### 1. Component Discovery
- Always search for components before building flows
- Use specific search terms for better results
- Check component documentation and examples

### 2. Flow Design
- Start with simple flows and add complexity gradually
- Validate flows before creation
- Use descriptive names and tags for organization

### 3. Testing
- Test flows immediately after creation
- Use different input types to verify behavior
- Monitor execution logs for debugging

### 4. Session Management
- Use consistent session IDs for conversations
- Clear sessions when conversations end
- Monitor session usage for performance

### 5. Error Handling
- Always wrap flow operations in try-catch blocks
- Check execution status for detailed error information
- Use validation tools before making changes

## Common Workflows

### 1. Creating a Chat Bot
1. Search for chat components
2. Get OpenAI/LLM component details
3. Create flow with input → LLM → output
4. Test with sample conversations
5. Add memory for conversation context

### 2. Building a RAG System
1. Find embedding and vector store components
2. Create document processing flow
3. Build query flow with retrieval
4. Test with sample documents and queries
5. Optimize retrieval parameters

### 3. Workflow Automation
1. Identify trigger components (webhook, schedule)
2. Find processing components for your use case
3. Add output components (email, API calls)
4. Create and test the automation flow
5. Monitor execution metrics

## Troubleshooting

### Common Issues

1. **"Component not found"**
   - Use `langflow_search_components` to find the correct name
   - Check if component is in expected category

2. **"Flow validation failed"**
   - Use `langflow_validate_flow` to get specific errors
   - Check node connections and required fields

3. **"Execution failed"**
   - Check `langflow_get_execution_status` for details
   - Verify API keys and external service connections

4. **"API connection failed"**
   - Verify Langflow is running and accessible
   - Check API key and URL configuration

### Getting Help

- Check the [Examples](examples/) directory for complete workflows
- Review component documentation using the discovery tools
- Monitor execution logs for debugging information
- Use validation tools before making changes
