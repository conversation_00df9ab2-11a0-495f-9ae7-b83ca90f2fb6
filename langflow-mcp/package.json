{"name": "langflow-mcp", "version": "1.0.0", "description": "Model Context Protocol (MCP) server for Langflow - enables AI agents to programmatically create, edit, and manage Langflow flows", "main": "dist/index.js", "bin": {"langflow-mcp": "./dist/mcp/index.js"}, "scripts": {"build": "tsc", "start": "node dist/mcp/index.js", "start:http": "LANGFLOW_MCP_MODE=http node dist/mcp/index.js", "dev": "npm run build && npm run start", "dev:http": "LANGFLOW_MCP_MODE=http nodemon --watch src --ext ts --exec 'npm run build && npm run start:http'", "test": "jest", "lint": "tsc --noEmit", "typecheck": "tsc --noEmit", "init-db": "node dist/scripts/init-database.js", "update-components": "node dist/scripts/update-components.js", "validate": "node dist/scripts/validate.js"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/langflow-mcp.git"}, "keywords": ["langflow", "mcp", "model-context-protocol", "ai", "workflow", "automation", "llm", "agents"], "author": "Your Name", "license": "MIT", "bugs": {"url": "https://github.com/your-org/langflow-mcp/issues"}, "homepage": "https://github.com/your-org/langflow-mcp#readme", "files": ["dist/**/*", "data/components.db", ".env.example", "README.md", "LICENSE"], "devDependencies": {"@types/node": "^22.15.30", "@types/jest": "^29.5.14", "@types/express": "^5.0.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.13.2", "axios": "^1.10.0", "better-sqlite3": "^11.10.0", "dotenv": "^16.5.0", "express": "^4.21.2", "uuid": "^10.0.0"}, "optionalDependencies": {"better-sqlite3": "^11.10.0"}}