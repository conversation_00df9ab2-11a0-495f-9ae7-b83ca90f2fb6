# Langflow MCP

A Model Context Protocol (MCP) server that provides AI assistants with comprehensive access to Langflow's workflow automation platform. This enables AI agents to programmatically create, edit, manage flows, variables, and everything else possible in Langflow.

## Overview

Langflow MCP serves as a bridge between Langflow's workflow automation platform and AI models, enabling them to understand and work with Langflow flows effectively. It provides structured access to:

- 🔧 **Flow Management** - Create, read, update, delete, and list Langflow flows
- 🔍 **Component Discovery** - Explore available components, their inputs/outputs, and capabilities  
- ⚡ **Flow Execution** - Run flows, monitor executions, handle streaming responses
- 📚 **Template Management** - Access and create reusable flow templates
- ✅ **Validation & Testing** - Validate flow configurations and test executions

## Features

- **🔍 Smart Component Discovery**: Find components by name, category, or functionality
- **📖 Comprehensive Flow Management**: Full CRUD operations for Langflow flows
- **⚡ Real-time Execution**: Run flows with streaming support and session management
- **✅ Advanced Validation**: Validate flow configurations before deployment
- **🌐 Universal Compatibility**: Works with any Langflow instance via REST API
- **🔗 Template System**: Access and create reusable flow templates

## Quick Start

### Option 1: npx (Fastest - No Installation!)

```bash
# Run directly with npx (no installation needed!)
npx langflow-mcp
```

Add to Claude Desktop config:

**Basic configuration (component discovery only):**

```json
{
  "mcpServers": {
    "langflow-mcp": {
      "command": "npx",
      "args": ["langflow-mcp"],
      "env": {
        "LANGFLOW_MCP_MODE": "stdio",
        "LOG_LEVEL": "error",
        "DISABLE_CONSOLE_OUTPUT": "true"
      }
    }
  }
}
```

**Full configuration (with Langflow management):**

```json
{
  "mcpServers": {
    "langflow-mcp": {
      "command": "npx",
      "args": ["langflow-mcp"],
      "env": {
        "LANGFLOW_MCP_MODE": "stdio",
        "LOG_LEVEL": "error",
        "DISABLE_CONSOLE_OUTPUT": "true",
        "LANGFLOW_API_URL": "http://localhost:7860",
        "LANGFLOW_API_KEY": "your-api-key"
      }
    }
  }
}
```

### Option 2: Local Installation

```bash
# Clone and setup
git clone https://github.com/your-org/langflow-mcp.git
cd langflow-mcp
npm install
npm run build

# Test it works
npm start
```

Add to Claude Desktop config:

```json
{
  "mcpServers": {
    "langflow-mcp": {
      "command": "node",
      "args": ["/absolute/path/to/langflow-mcp/dist/mcp/index.js"],
      "env": {
        "LANGFLOW_MCP_MODE": "stdio",
        "LOG_LEVEL": "error",
        "DISABLE_CONSOLE_OUTPUT": "true",
        "LANGFLOW_API_URL": "http://localhost:7860",
        "LANGFLOW_API_KEY": "your-api-key"
      }
    }
  }
}
```

### Option 3: HTTP Mode (Remote Access)

```bash
# Start HTTP server
LANGFLOW_MCP_MODE=http npm start
```

The server will start on `http://localhost:3000` with endpoints:
- `/health` - Health check
- `/mcp` - MCP protocol endpoint  
- `/docs` - API documentation

## Configuration

Create a `.env` file based on `.env.example`:

```bash
# Required for flow management
LANGFLOW_API_URL=http://localhost:7860
LANGFLOW_API_KEY=your-api-key-here

# Optional settings
LANGFLOW_MCP_MODE=stdio  # stdio or http
LOG_LEVEL=error          # error, warn, info, debug
COMPONENT_CACHE_TTL=3600 # Component cache TTL in seconds
```

## Available MCP Tools

### Flow Management Tools
- `langflow_list_flows` - List all flows with filtering
- `langflow_get_flow` - Get detailed flow information
- `langflow_create_flow` - Create new flows
- `langflow_update_flow` - Update existing flows
- `langflow_delete_flow` - Delete flows
- `langflow_duplicate_flow` - Duplicate flows
- `langflow_validate_flow` - Validate flow configurations

### Component Discovery Tools
- `langflow_list_components` - List available components by category
- `langflow_search_components` - Search components by functionality
- `langflow_get_component_details` - Get detailed component information
- `langflow_get_component_categories` - Get component categories
- `langflow_validate_component_config` - Validate component configurations

### Flow Execution Tools
- `langflow_run_flow` - Execute flows with inputs
- `langflow_run_flow_stream` - Execute flows with streaming
- `langflow_trigger_webhook` - Trigger flows via webhook
- `langflow_get_execution_status` - Check execution status
- `langflow_list_executions` - List recent executions

## Example Usage

### Creating a Simple Flow

```javascript
// 1. Discover available components
langflow_search_components({ query: "chat input" })

// 2. Get component details
langflow_get_component_details({ component_name: "ChatInput" })

// 3. Create a flow
langflow_create_flow({
  name: "Simple Chat Flow",
  data: {
    nodes: [
      {
        id: "chat-input-1",
        type: "ChatInput",
        position: [100, 100],
        data: { /* component configuration */ }
      },
      {
        id: "chat-output-1", 
        type: "ChatOutput",
        position: [300, 100],
        data: { /* component configuration */ }
      }
    ],
    edges: [
      {
        id: "edge-1",
        source: "chat-input-1",
        target: "chat-output-1"
      }
    ]
  }
})

// 4. Run the flow
langflow_run_flow({
  flow_id_or_name: "flow-id",
  input_value: "Hello, world!"
})
```

## Development

```bash
# Install dependencies
npm install

# Build TypeScript
npm run build

# Run in development mode
npm run dev

# Run tests
npm test

# Type checking
npm run typecheck
```

## Architecture

```
langflow-mcp/
├── src/
│   ├── mcp/                 # MCP server implementation
│   │   ├── server.ts        # Main MCP server
│   │   ├── tools/           # MCP tool definitions
│   │   └── handlers/        # Tool execution handlers
│   ├── services/            # Core services
│   │   ├── langflow_api.ts  # Langflow API client
│   │   └── component_registry.ts # Component cache
│   ├── models/              # Data models
│   └── utils/               # Utilities
├── data/                    # Component cache and templates
└── docs/                    # Documentation
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Run tests (`npm test`)
4. Submit a pull request

## License

MIT License - see [LICENSE](LICENSE) for details.

## Acknowledgments

- [Langflow](https://langflow.org) team for the workflow automation platform
- [Anthropic](https://anthropic.com) for the Model Context Protocol
- Inspired by [n8n-mcp](https://github.com/czlonkowski/n8n-mcp) project

---

**Built with ❤️ for the Langflow community**  
Making AI + Langflow workflow creation delightful
