export interface ComponentInput {
  name: string;
  display_name: string;
  type: string;
  required?: boolean;
  placeholder?: string;
  multiline?: boolean;
  password?: boolean;
  options?: string[] | Record<string, any>[];
  list?: boolean;
  show?: boolean;
  advanced?: boolean;
  info?: string;
  value?: any;
  file_types?: string[];
  suffixes?: string[];
  input_types?: string[];
  tool_mode?: boolean;
  refresh_button?: boolean;
  refresh_button_text?: string;
  real_time_refresh?: boolean;
  load_from_db?: boolean;
  title_case?: boolean;
}

export interface ComponentOutput {
  name: string;
  display_name: string;
  type: string;
  method?: string;
  cache?: boolean;
  hidden?: boolean;
}

export interface ComponentTemplate {
  display_name: string;
  description: string;
  icon?: string;
  is_input?: boolean;
  is_output?: boolean;
  is_composition?: boolean;
  template: Record<string, ComponentInput>;
  outputs?: ComponentOutput[];
  documentation?: string;
  custom_fields?: Record<string, any>;
  output_types?: string[];
  field_formatters?: Record<string, any>;
  beta?: boolean;
  error?: string;
  flow?: boolean;
  conditional_paths?: string[];
  frozen?: boolean;
  legacy?: boolean;
}

export interface Component {
  name: string;
  display_name: string;
  description: string;
  category: string;
  subcategory?: string;
  template: ComponentTemplate;
  base_classes: string[];
  documentation?: string;
  icon?: string;
  is_input?: boolean;
  is_output?: boolean;
  is_composition?: boolean;
  beta?: boolean;
  legacy?: boolean;
  custom?: boolean;
  flow?: boolean;
  error?: string;
}

export interface ComponentCategory {
  name: string;
  display_name: string;
  description?: string;
  components: Component[];
  subcategories?: ComponentCategory[];
}

export interface ComponentSearchResult {
  component: Component;
  relevance: number;
  matches: {
    name?: boolean;
    display_name?: boolean;
    description?: boolean;
    category?: boolean;
    documentation?: boolean;
  };
}

export interface ComponentUsageExample {
  title: string;
  description: string;
  flow: {
    nodes: any[];
    edges: any[];
  };
  inputs: Record<string, any>;
  expected_outputs: Record<string, any>;
}

export interface ComponentMetadata {
  component: Component;
  usage_examples?: ComponentUsageExample[];
  common_patterns?: string[];
  related_components?: string[];
  troubleshooting?: Array<{
    issue: string;
    solution: string;
  }>;
  performance_notes?: string[];
  version_history?: Array<{
    version: string;
    changes: string[];
    breaking_changes?: string[];
  }>;
}

export interface ComponentValidationRule {
  field: string;
  rule: 'required' | 'type' | 'format' | 'range' | 'custom';
  value?: any;
  message: string;
  severity: 'error' | 'warning' | 'info';
}

export interface ComponentValidationResult {
  valid: boolean;
  errors: Array<{
    field: string;
    message: string;
    value?: any;
  }>;
  warnings: Array<{
    field: string;
    message: string;
    value?: any;
  }>;
  suggestions?: Array<{
    field: string;
    message: string;
    suggested_value?: any;
  }>;
}
