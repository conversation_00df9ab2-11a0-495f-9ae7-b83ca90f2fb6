export interface FlowNode {
  id: string;
  type: string;
  position: [number, number];
  data: {
    type: string;
    node: {
      template: Record<string, any>;
      description: string;
      base_classes: string[];
      name?: string;
      display_name?: string;
      documentation?: string;
      custom_fields?: Record<string, any>;
      output_types?: string[];
      field_formatters?: Record<string, any>;
      beta?: boolean;
      error?: string;
      flow?: boolean;
    };
    value?: any;
    id: string;
  };
  width?: number;
  height?: number;
  selected?: boolean;
  positionAbsolute?: [number, number];
  dragging?: boolean;
}

export interface FlowEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
  type?: string;
  animated?: boolean;
  style?: Record<string, any>;
  data?: Record<string, any>;
}

export interface FlowData {
  nodes: FlowNode[];
  edges: FlowEdge[];
  viewport?: {
    x: number;
    y: number;
    zoom: number;
  };
}

export interface Flow {
  id: string;
  name: string;
  description?: string;
  data: FlowData;
  is_component?: boolean;
  updated_at?: string;
  folder_id?: string;
  user_id?: string;
  endpoint_name?: string;
  tags?: string[];
  flows?: Flow[];
  webhook?: boolean;
  status?: string;
}

export interface FlowCreate {
  name: string;
  description?: string;
  data: FlowData;
  is_component?: boolean;
  folder_id?: string;
  endpoint_name?: string;
  tags?: string[];
  webhook?: boolean;
}

export interface FlowUpdate {
  name?: string;
  description?: string;
  data?: FlowData;
  is_component?: boolean;
  folder_id?: string;
  endpoint_name?: string;
  tags?: string[];
  webhook?: boolean;
}

export interface FlowExecution {
  id: string;
  flow_id: string;
  status: 'running' | 'success' | 'error' | 'cancelled';
  inputs?: Record<string, any>;
  outputs?: Record<string, any>;
  error?: string;
  started_at: string;
  ended_at?: string;
  session_id?: string;
  user_id?: string;
}

export interface FlowExecutionRequest {
  input_value?: string;
  input_type?: string;
  output_type?: string;
  output_component?: string;
  session_id?: string;
  tweaks?: Record<string, any>;
  stream?: boolean;
  inputs?: Array<{
    components: string[];
    input_value: string;
  }>;
  outputs?: string[];
}

export interface FlowExecutionResponse {
  session_id: string;
  outputs: Array<{
    inputs: Record<string, any>;
    outputs: Array<{
      results: Record<string, any>;
      artifacts?: Record<string, any>;
      messages?: Array<{
        message: string;
        type: string;
        component_id?: string;
      }>;
      component_display_name: string;
      component_id: string;
    }>;
  }>;
}

export interface FlowTemplate {
  id: string;
  name: string;
  description: string;
  flow: Flow;
  tags: string[];
  category: string;
  author?: string;
  version?: string;
  created_at?: string;
  updated_at?: string;
}

export interface FlowValidationResult {
  valid: boolean;
  errors: Array<{
    node_id?: string;
    message: string;
    type: 'error' | 'warning' | 'info';
  }>;
  warnings: Array<{
    node_id?: string;
    message: string;
    type: 'warning';
  }>;
  suggestions?: Array<{
    node_id?: string;
    message: string;
    type: 'suggestion';
  }>;
}
