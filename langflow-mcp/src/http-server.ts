import express from 'express';
import { LangflowMCPServer } from './mcp/server';
import { logger } from './utils/logger';
import { getConfig } from './utils/config';

export async function startHTTPServer(): Promise<void> {
  const config = getConfig();
  const app = express();
  
  // Middleware
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));
  
  // CORS middleware
  app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    
    if (req.method === 'OPTIONS') {
      res.sendStatus(200);
    } else {
      next();
    }
  });

  // Health check endpoint
  app.get('/health', (req, res) => {
    res.json({
      status: 'healthy',
      service: 'langflow-mcp',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
    });
  });

  // MCP endpoint
  app.post('/mcp', async (req, res) => {
    try {
      // Create a new MCP server instance for each request
      const mcpServer = new LangflowMCPServer();
      
      // Handle MCP request
      const { method, params } = req.body;
      
      let result;
      switch (method) {
        case 'initialize':
          result = {
            protocolVersion: '2024-11-05',
            capabilities: { tools: {} },
            serverInfo: { name: 'langflow-mcp', version: '1.0.0' },
          };
          break;
          
        case 'tools/list':
          // Get available tools
          result = { tools: [] }; // This would need to be implemented
          break;
          
        case 'tools/call':
          // Execute tool
          result = await mcpServer.executeTool(params.name, params.arguments);
          break;
          
        default:
          throw new Error(`Unknown method: ${method}`);
      }
      
      res.json({
        jsonrpc: '2.0',
        id: req.body.id,
        result,
      });
    } catch (error) {
      logger.error('HTTP MCP request error:', error);
      
      res.status(500).json({
        jsonrpc: '2.0',
        id: req.body.id,
        error: {
          code: -32603,
          message: error instanceof Error ? error.message : 'Internal error',
        },
      });
    }
  });

  // API documentation endpoint
  app.get('/docs', (req, res) => {
    res.json({
      service: 'Langflow MCP Server',
      version: '1.0.0',
      description: 'Model Context Protocol server for Langflow integration',
      endpoints: {
        '/health': 'GET - Health check',
        '/mcp': 'POST - MCP protocol endpoint',
        '/docs': 'GET - This documentation',
      },
      mcp_tools: {
        flow_management: [
          'langflow_list_flows',
          'langflow_get_flow',
          'langflow_create_flow',
          'langflow_update_flow',
          'langflow_delete_flow',
          'langflow_duplicate_flow',
          'langflow_validate_flow',
        ],
        component_discovery: [
          'langflow_list_components',
          'langflow_search_components',
          'langflow_get_component_details',
          'langflow_get_component_categories',
        ],
        flow_execution: [
          'langflow_run_flow',
          'langflow_run_flow_stream',
          'langflow_trigger_webhook',
          'langflow_get_execution_status',
          'langflow_list_executions',
        ],
      },
      configuration: {
        required_env_vars: [
          'LANGFLOW_API_URL',
          'LANGFLOW_API_KEY',
        ],
        optional_env_vars: [
          'LOG_LEVEL',
          'COMPONENT_CACHE_TTL',
          'DEFAULT_SESSION_TIMEOUT',
        ],
      },
    });
  });

  // Error handling middleware
  app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
    logger.error('Express error:', error);
    
    res.status(500).json({
      error: 'Internal server error',
      message: error.message,
      timestamp: new Date().toISOString(),
    });
  });

  // 404 handler
  app.use((req, res) => {
    res.status(404).json({
      error: 'Not found',
      message: `Endpoint ${req.method} ${req.path} not found`,
      available_endpoints: ['/health', '/mcp', '/docs'],
    });
  });

  // Start server
  const server = app.listen(config.httpPort, config.httpHost, () => {
    logger.info(`Langflow MCP HTTP Server listening on http://${config.httpHost}:${config.httpPort}`);
    console.log(`Langflow MCP HTTP Server started on http://${config.httpHost}:${config.httpPort}`);
    console.log(`Health check: http://${config.httpHost}:${config.httpPort}/health`);
    console.log(`Documentation: http://${config.httpHost}:${config.httpPort}/docs`);
  });

  // Graceful shutdown
  const shutdown = async () => {
    logger.info('Shutting down HTTP server...');
    server.close(() => {
      logger.info('HTTP server closed');
      process.exit(0);
    });
  };

  process.on('SIGTERM', shutdown);
  process.on('SIGINT', shutdown);
}
