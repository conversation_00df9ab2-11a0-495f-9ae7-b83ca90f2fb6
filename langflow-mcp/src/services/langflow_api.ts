import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { logger } from '../utils/logger';
import { getLangflowApiConfig } from '../utils/config';
import { Flow, FlowCreate, FlowUpdate, FlowExecution, FlowExecutionRequest, FlowExecutionResponse } from '../models/flow';
import { Component } from '../models/component';

export class LangflowApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public response?: any
  ) {
    super(message);
    this.name = 'LangflowApiError';
  }
}

export class LangflowApiClient {
  private client: AxiosInstance;
  private baseUrl: string;
  private apiKey: string;

  constructor() {
    const config = getLangflowApiConfig();
    if (!config) {
      throw new Error('Langflow API not configured. Please set LANGFLOW_API_URL and LANGFLOW_API_KEY');
    }

    this.baseUrl = config.url.replace(/\/$/, ''); // Remove trailing slash
    this.apiKey = config.apiKey;

    this.client = axios.create({
      baseURL: `${this.baseUrl}/api/v1`,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': this.apiKey,
      },
      timeout: 30000, // 30 second timeout
    });

    // Add request/response interceptors for logging and error handling
    this.client.interceptors.request.use(
      (config) => {
        logger.debug(`API Request: ${config.method?.toUpperCase()} ${config.url}`, {
          data: config.data,
          params: config.params,
        });
        return config;
      },
      (error) => {
        logger.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    this.client.interceptors.response.use(
      (response) => {
        logger.debug(`API Response: ${response.status} ${response.config.url}`, {
          data: response.data,
        });
        return response;
      },
      (error) => {
        const message = error.response?.data?.detail || error.message;
        const statusCode = error.response?.status;
        logger.error(`API Error: ${statusCode} ${message}`, {
          url: error.config?.url,
          method: error.config?.method,
          data: error.response?.data,
        });
        throw new LangflowApiError(message, statusCode, error.response?.data);
      }
    );
  }

  // Health check
  async healthCheck(): Promise<{ status: string; version?: string }> {
    try {
      const response = await this.client.get('/version');
      return { status: 'healthy', version: response.data.version };
    } catch (error) {
      logger.error('Health check failed:', error);
      throw new LangflowApiError('Langflow API health check failed');
    }
  }

  // Flow Management
  async listFlows(params?: {
    limit?: number;
    offset?: number;
    folder_id?: string;
    components_only?: boolean;
    header_flows?: boolean;
  }): Promise<Flow[]> {
    const response = await this.client.get('/flows', { params });
    return response.data;
  }

  async getFlow(flowId: string): Promise<Flow> {
    const response = await this.client.get(`/flows/${flowId}`);
    return response.data;
  }

  async createFlow(flow: FlowCreate): Promise<Flow> {
    const response = await this.client.post('/flows', flow);
    return response.data;
  }

  async updateFlow(flowId: string, flow: FlowUpdate): Promise<Flow> {
    const response = await this.client.patch(`/flows/${flowId}`, flow);
    return response.data;
  }

  async deleteFlow(flowId: string): Promise<void> {
    await this.client.delete(`/flows/${flowId}`);
  }

  async duplicateFlow(flowId: string, name?: string): Promise<Flow> {
    const originalFlow = await this.getFlow(flowId);
    const duplicatedFlow: FlowCreate = {
      name: name || `${originalFlow.name} (Copy)`,
      description: originalFlow.description,
      data: originalFlow.data,
      is_component: originalFlow.is_component,
      tags: originalFlow.tags,
    };
    return this.createFlow(duplicatedFlow);
  }

  // Flow Execution
  async runFlow(
    flowIdOrName: string,
    request: FlowExecutionRequest
  ): Promise<FlowExecutionResponse> {
    const response = await this.client.post(`/run/${flowIdOrName}`, request);
    return response.data;
  }

  async runFlowStream(
    flowIdOrName: string,
    request: FlowExecutionRequest
  ): Promise<AsyncIterable<any>> {
    const response = await this.client.post(`/run/${flowIdOrName}?stream=true`, request, {
      responseType: 'stream',
    });

    return this.parseStreamResponse(response.data);
  }

  private async* parseStreamResponse(stream: any): AsyncIterable<any> {
    let buffer = '';
    
    for await (const chunk of stream) {
      buffer += chunk.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6));
            yield data;
          } catch (error) {
            logger.warn('Failed to parse stream data:', line);
          }
        }
      }
    }
  }

  async triggerWebhook(
    flowIdOrName: string,
    data: any,
    headers?: Record<string, string>
  ): Promise<{ message: string; status: string }> {
    const response = await this.client.post(`/webhook/${flowIdOrName}`, data, {
      headers: headers || {},
    });
    return response.data;
  }

  // Component Discovery
  async getAllComponents(): Promise<Record<string, Record<string, Component>>> {
    const response = await this.client.get('/all');
    return response.data;
  }

  async getComponentsByCategory(category: string): Promise<Record<string, Component>> {
    const allComponents = await this.getAllComponents();
    return allComponents[category] || {};
  }

  // Validation
  async validateFlow(flowData: any): Promise<{ valid: boolean; errors: any[] }> {
    try {
      const response = await this.client.post('/validate/flow', { flow: flowData });
      return { valid: true, errors: [] };
    } catch (error) {
      if (error instanceof LangflowApiError && error.statusCode === 400) {
        return { valid: false, errors: [error.response] };
      }
      throw error;
    }
  }

  // Utility methods
  getBaseUrl(): string {
    return this.baseUrl;
  }

  getApiKey(): string {
    return this.apiKey;
  }

  // Test connection
  async testConnection(): Promise<boolean> {
    try {
      await this.healthCheck();
      return true;
    } catch (error) {
      return false;
    }
  }
}
