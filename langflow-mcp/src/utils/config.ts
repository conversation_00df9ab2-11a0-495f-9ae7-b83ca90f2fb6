import { config } from 'dotenv';
import { existsSync } from 'fs';
import { logger } from './logger';

// Load environment variables
config();

export interface LangflowMCPConfig {
  // MCP Server Configuration
  mode: 'stdio' | 'http';
  logLevel: string;
  disableConsoleOutput: boolean;

  // Langflow API Configuration
  langflowApiUrl?: string;
  langflowApiKey?: string;

  // Database Configuration
  dbPath: string;

  // Component Cache Settings
  componentCacheTTL: number;
  autoUpdateComponents: boolean;

  // Flow Execution Settings
  defaultSessionTimeout: number;
  maxConcurrentExecutions: number;

  // Template Settings
  templatesPath: string;

  // HTTP Server Settings (for http mode)
  httpPort: number;
  httpHost: string;
}

class ConfigManager {
  private config: LangflowMCPConfig;

  constructor() {
    this.config = this.loadConfig();
    this.validateConfig();
  }

  private loadConfig(): LangflowMCPConfig {
    return {
      // MCP Server Configuration
      mode: (process.env.LANGFLOW_MCP_MODE as 'stdio' | 'http') || 'stdio',
      logLevel: process.env.LOG_LEVEL || 'error',
      disableConsoleOutput: process.env.DISABLE_CONSOLE_OUTPUT === 'true',

      // Langflow API Configuration
      langflowApiUrl: process.env.LANGFLOW_API_URL,
      langflowApiKey: process.env.LANGFLOW_API_KEY,

      // Database Configuration
      dbPath: process.env.LANGFLOW_MCP_DB_PATH || './data/components.db',

      // Component Cache Settings
      componentCacheTTL: parseInt(process.env.COMPONENT_CACHE_TTL || '3600'),
      autoUpdateComponents: process.env.AUTO_UPDATE_COMPONENTS !== 'false',

      // Flow Execution Settings
      defaultSessionTimeout: parseInt(process.env.DEFAULT_SESSION_TIMEOUT || '300'),
      maxConcurrentExecutions: parseInt(process.env.MAX_CONCURRENT_EXECUTIONS || '5'),

      // Template Settings
      templatesPath: process.env.TEMPLATES_PATH || './data/templates',

      // HTTP Server Settings
      httpPort: parseInt(process.env.HTTP_PORT || '3000'),
      httpHost: process.env.HTTP_HOST || 'localhost',
    };
  }

  private validateConfig(): void {
    // Validate mode
    if (!['stdio', 'http'].includes(this.config.mode)) {
      throw new Error(`Invalid LANGFLOW_MCP_MODE: ${this.config.mode}. Must be 'stdio' or 'http'`);
    }

    // Validate numeric values
    if (this.config.componentCacheTTL < 0) {
      throw new Error('COMPONENT_CACHE_TTL must be a positive number');
    }

    if (this.config.defaultSessionTimeout < 0) {
      throw new Error('DEFAULT_SESSION_TIMEOUT must be a positive number');
    }

    if (this.config.maxConcurrentExecutions < 1) {
      throw new Error('MAX_CONCURRENT_EXECUTIONS must be at least 1');
    }

    if (this.config.httpPort < 1 || this.config.httpPort > 65535) {
      throw new Error('HTTP_PORT must be between 1 and 65535');
    }

    // Log configuration status
    logger.info('Configuration loaded successfully', {
      mode: this.config.mode,
      hasLangflowApi: !!this.config.langflowApiUrl,
      dbPath: this.config.dbPath,
      templatesPath: this.config.templatesPath,
    });
  }

  get(): LangflowMCPConfig {
    return { ...this.config };
  }

  isLangflowApiConfigured(): boolean {
    return !!(this.config.langflowApiUrl && this.config.langflowApiKey);
  }

  getLangflowApiConfig(): { url: string; apiKey: string } | null {
    if (!this.isLangflowApiConfigured()) {
      return null;
    }
    return {
      url: this.config.langflowApiUrl!,
      apiKey: this.config.langflowApiKey!,
    };
  }

  update(updates: Partial<LangflowMCPConfig>): void {
    this.config = { ...this.config, ...updates };
    this.validateConfig();
  }
}

export const configManager = new ConfigManager();
export const getConfig = () => configManager.get();
export const isLangflowApiConfigured = () => configManager.isLangflowApiConfigured();
export const getLangflowApiConfig = () => configManager.getLangflowApiConfig();
