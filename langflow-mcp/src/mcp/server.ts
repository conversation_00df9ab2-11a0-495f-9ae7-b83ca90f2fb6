import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  InitializeRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import { logger } from '../utils/logger';
import { getConfig, isLangflowApiConfigured } from '../utils/config';
import { LangflowApiClient } from '../services/langflow_api';
import { flowManagementTools } from './tools/flow_management';
import { componentDiscoveryTools } from './tools/component_discovery';
import { flowExecutionTools } from './tools/flow_execution';
import { FlowManagementHandlers } from './handlers/flow_handlers';
import { ComponentDiscoveryHandlers } from './handlers/component_handlers';
import { FlowExecutionHandlers } from './handlers/execution_handlers';

export class LangflowMCPServer {
  private server: Server;
  private apiClient: LangflowApiClient | null = null;
  private flowHandlers: FlowManagementHandlers | null = null;
  private componentHandlers: ComponentDiscoveryHandlers | null = null;
  private executionHandlers: FlowExecutionHandlers | null = null;
  private initialized: Promise<void>;

  constructor() {
    logger.info('Initializing Langflow MCP Server');
    
    const config = getConfig();
    const apiConfigured = isLangflowApiConfigured();
    
    // Initialize API client if configured
    if (apiConfigured) {
      try {
        this.apiClient = new LangflowApiClient();
        this.flowHandlers = new FlowManagementHandlers(this.apiClient);
        this.componentHandlers = new ComponentDiscoveryHandlers(this.apiClient);
        this.executionHandlers = new FlowExecutionHandlers(this.apiClient);
        logger.info('Langflow API client initialized successfully');
      } catch (error) {
        logger.error('Failed to initialize Langflow API client:', error);
        throw error;
      }
    } else {
      logger.warn('Langflow API not configured. Only documentation tools will be available.');
    }

    // Initialize MCP server
    this.server = new Server(
      {
        name: 'langflow-mcp',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.initialized = this.initializeServer();
    this.setupHandlers();
  }

  private async initializeServer(): Promise<void> {
    try {
      // Test API connection if configured
      if (this.apiClient) {
        const isConnected = await this.apiClient.testConnection();
        if (!isConnected) {
          logger.error('Failed to connect to Langflow API');
          throw new Error('Langflow API connection failed');
        }
        logger.info('Langflow API connection verified');
      }

      // Initialize component cache
      if (this.componentHandlers) {
        await this.componentHandlers.initializeComponentCache();
      }

      logger.info('Langflow MCP Server initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize server:', error);
      throw error;
    }
  }

  private setupHandlers(): void {
    // Handle initialization
    this.server.setRequestHandler(InitializeRequestSchema, async () => {
      const config = getConfig();
      const apiConfigured = isLangflowApiConfigured();
      
      const totalTools = this.getAvailableTools().length;
      
      logger.info(`MCP server initialized with ${totalTools} tools (Langflow API: ${apiConfigured ? 'configured' : 'not configured'})`);

      return {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: {},
        },
        serverInfo: {
          name: 'langflow-mcp',
          version: '1.0.0',
        },
      };
    });

    // Handle tool listing
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      const tools = this.getAvailableTools();
      
      logger.debug(`Tool listing: ${tools.length} tools available`);
      
      return { tools };
    });

    // Handle tool execution
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      
      try {
        logger.debug(`Executing tool: ${name}`, { args });
        
        const result = await this.executeTool(name, args);
        
        logger.debug(`Tool ${name} executed successfully`);
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(result, null, 2),
            },
          ],
        };
      } catch (error) {
        logger.error(`Error executing tool ${name}:`, error);
        
        return {
          content: [
            {
              type: 'text',
              text: `Error executing tool ${name}: ${error instanceof Error ? error.message : 'Unknown error'}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  private getAvailableTools() {
    const tools = [...componentDiscoveryTools];
    
    // Add flow management and execution tools if API is configured
    if (isLangflowApiConfigured()) {
      tools.push(...flowManagementTools, ...flowExecutionTools);
    }
    
    return tools;
  }

  private async executeTool(name: string, args: any): Promise<any> {
    // Ensure server is initialized
    await this.initialized;

    // Component discovery tools (always available)
    if (name.startsWith('langflow_list_components') || 
        name.startsWith('langflow_search_components') ||
        name.startsWith('langflow_get_component')) {
      if (!this.componentHandlers) {
        throw new Error('Component handlers not initialized');
      }
      return this.componentHandlers.handleTool(name, args);
    }

    // Flow management tools (require API)
    if (name.startsWith('langflow_') && 
        (name.includes('flow') && !name.includes('run_flow'))) {
      if (!this.flowHandlers) {
        throw new Error('Langflow API not configured. Please set LANGFLOW_API_URL and LANGFLOW_API_KEY');
      }
      return this.flowHandlers.handleTool(name, args);
    }

    // Flow execution tools (require API)
    if (name.startsWith('langflow_run_') || 
        name.startsWith('langflow_trigger_') ||
        name.startsWith('langflow_get_execution') ||
        name.startsWith('langflow_list_executions') ||
        name.startsWith('langflow_cancel_') ||
        name.startsWith('langflow_clear_') ||
        name.startsWith('langflow_test_') ||
        name.startsWith('langflow_get_flow_')) {
      if (!this.executionHandlers) {
        throw new Error('Langflow API not configured. Please set LANGFLOW_API_URL and LANGFLOW_API_KEY');
      }
      return this.executionHandlers.handleTool(name, args);
    }

    throw new Error(`Unknown tool: ${name}`);
  }

  async run(): Promise<void> {
    // Ensure server is initialized
    await this.initialized;
    
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    
    logger.info('Langflow MCP Server running on stdio transport');
    
    // Keep the process alive
    process.stdin.resume();
  }

  async shutdown(): Promise<void> {
    logger.info('Shutting down Langflow MCP Server...');
    
    // Close any open connections
    if (this.apiClient) {
      // Add cleanup if needed
    }
    
    logger.info('Langflow MCP Server shutdown complete');
  }
}
