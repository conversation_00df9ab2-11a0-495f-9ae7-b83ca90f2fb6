import { ToolDefinition } from '@modelcontextprotocol/sdk/types.js';

/**
 * Langflow Component Discovery MCP Tools
 * 
 * These tools help AI agents discover and explore available Langflow components,
 * their inputs, outputs, and capabilities.
 */
export const componentDiscoveryTools: ToolDefinition[] = [
  {
    name: 'langflow_list_components',
    description: `List all available Langflow components organized by category. Use this to discover what components are available for building flows.`,
    inputSchema: {
      type: 'object',
      properties: {
        category: {
          type: 'string',
          description: 'Filter by specific category (e.g., "llms", "chains", "agents", "tools", "embeddings")',
        },
        include_custom: {
          type: 'boolean',
          description: 'Include custom components (default: true)',
          default: true,
        },
        include_beta: {
          type: 'boolean',
          description: 'Include beta/experimental components (default: false)',
          default: false,
        },
      },
    },
  },

  {
    name: 'langflow_search_components',
    description: `Search for components by name, description, or functionality. Returns ranked results based on relevance.`,
    inputSchema: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'Search query (component name, description, or functionality)',
        },
        limit: {
          type: 'number',
          description: 'Maximum number of results to return (default: 20)',
          default: 20,
        },
        category: {
          type: 'string',
          description: 'Limit search to specific category',
        },
        include_documentation: {
          type: 'boolean',
          description: 'Include component documentation in search (default: true)',
          default: true,
        },
      },
      required: ['query'],
    },
  },

  {
    name: 'langflow_get_component_details',
    description: `Get detailed information about a specific component including inputs, outputs, documentation, and usage examples.`,
    inputSchema: {
      type: 'object',
      properties: {
        component_name: {
          type: 'string',
          description: 'Name of the component to get details for',
        },
        category: {
          type: 'string',
          description: 'Category of the component (helps with disambiguation)',
        },
        include_examples: {
          type: 'boolean',
          description: 'Include usage examples (default: true)',
          default: true,
        },
        include_template: {
          type: 'boolean',
          description: 'Include full component template (default: true)',
          default: true,
        },
      },
      required: ['component_name'],
    },
  },

  {
    name: 'langflow_get_component_categories',
    description: `Get all available component categories with descriptions and component counts.`,
    inputSchema: {
      type: 'object',
      properties: {
        include_counts: {
          type: 'boolean',
          description: 'Include component counts per category (default: true)',
          default: true,
        },
        include_subcategories: {
          type: 'boolean',
          description: 'Include subcategory information (default: true)',
          default: true,
        },
      },
    },
  },

  {
    name: 'langflow_get_component_inputs',
    description: `Get detailed information about a component's input parameters including types, requirements, and validation rules.`,
    inputSchema: {
      type: 'object',
      properties: {
        component_name: {
          type: 'string',
          description: 'Name of the component',
        },
        category: {
          type: 'string',
          description: 'Category of the component',
        },
        input_name: {
          type: 'string',
          description: 'Specific input to get details for (optional, returns all if not specified)',
        },
      },
      required: ['component_name'],
    },
  },

  {
    name: 'langflow_get_component_outputs',
    description: `Get information about a component's outputs including types and connection possibilities.`,
    inputSchema: {
      type: 'object',
      properties: {
        component_name: {
          type: 'string',
          description: 'Name of the component',
        },
        category: {
          type: 'string',
          description: 'Category of the component',
        },
      },
      required: ['component_name'],
    },
  },

  {
    name: 'langflow_find_compatible_components',
    description: `Find components that can connect to a given component's outputs or provide inputs for a component.`,
    inputSchema: {
      type: 'object',
      properties: {
        component_name: {
          type: 'string',
          description: 'Name of the reference component',
        },
        connection_type: {
          type: 'string',
          enum: ['inputs', 'outputs'],
          description: 'Find components that can provide inputs or accept outputs',
        },
        output_type: {
          type: 'string',
          description: 'Specific output type to find compatible components for',
        },
        limit: {
          type: 'number',
          description: 'Maximum number of compatible components to return (default: 10)',
          default: 10,
        },
      },
      required: ['component_name', 'connection_type'],
    },
  },

  {
    name: 'langflow_get_component_examples',
    description: `Get usage examples and common patterns for a specific component.`,
    inputSchema: {
      type: 'object',
      properties: {
        component_name: {
          type: 'string',
          description: 'Name of the component',
        },
        example_type: {
          type: 'string',
          enum: ['basic', 'advanced', 'integration', 'all'],
          description: 'Type of examples to return (default: all)',
          default: 'all',
        },
        include_flow_snippets: {
          type: 'boolean',
          description: 'Include complete flow examples (default: true)',
          default: true,
        },
      },
      required: ['component_name'],
    },
  },

  {
    name: 'langflow_validate_component_config',
    description: `Validate a component configuration before using it in a flow. Checks required fields, data types, and constraints.`,
    inputSchema: {
      type: 'object',
      properties: {
        component_name: {
          type: 'string',
          description: 'Name of the component to validate',
        },
        config: {
          type: 'object',
          description: 'Component configuration to validate',
        },
        strict: {
          type: 'boolean',
          description: 'Use strict validation (default: false)',
          default: false,
        },
      },
      required: ['component_name', 'config'],
    },
  },

  {
    name: 'langflow_get_component_documentation',
    description: `Get comprehensive documentation for a component including description, parameters, examples, and troubleshooting.`,
    inputSchema: {
      type: 'object',
      properties: {
        component_name: {
          type: 'string',
          description: 'Name of the component',
        },
        format: {
          type: 'string',
          enum: ['markdown', 'json', 'text'],
          description: 'Documentation format (default: markdown)',
          default: 'markdown',
        },
        include_api_reference: {
          type: 'boolean',
          description: 'Include API reference information (default: true)',
          default: true,
        },
      },
      required: ['component_name'],
    },
  },
];
