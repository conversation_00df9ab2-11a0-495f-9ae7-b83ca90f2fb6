import { ToolDefinition } from '@modelcontextprotocol/sdk/types.js';

/**
 * Langflow Flow Execution MCP Tools
 * 
 * These tools enable AI agents to execute flows, monitor executions,
 * and handle streaming responses from Langflow.
 */
export const flowExecutionTools: ToolDefinition[] = [
  {
    name: 'langflow_run_flow',
    description: `Execute a flow by ID or name. Supports both simple input/output and complex multi-input scenarios. Returns execution results.`,
    inputSchema: {
      type: 'object',
      properties: {
        flow_id_or_name: {
          type: 'string',
          description: 'Flow ID or endpoint name to execute',
        },
        input_value: {
          type: 'string',
          description: 'Simple input value for flows with single input',
        },
        input_type: {
          type: 'string',
          enum: ['chat', 'text', 'json'],
          description: 'Type of input being provided (default: chat)',
          default: 'chat',
        },
        output_type: {
          type: 'string',
          enum: ['chat', 'text', 'json', 'all'],
          description: 'Expected output type (default: all)',
          default: 'all',
        },
        output_component: {
          type: 'string',
          description: 'Specific output component to return results from',
        },
        session_id: {
          type: 'string',
          description: 'Session ID for conversation context (auto-generated if not provided)',
        },
        tweaks: {
          type: 'object',
          description: 'Component parameter overrides',
          additionalProperties: true,
        },
        inputs: {
          type: 'array',
          description: 'Multiple inputs for complex flows',
          items: {
            type: 'object',
            properties: {
              components: {
                type: 'array',
                items: { type: 'string' },
                description: 'Target component IDs for this input',
              },
              input_value: {
                type: 'string',
                description: 'Input value',
              },
            },
            required: ['components', 'input_value'],
          },
        },
        outputs: {
          type: 'array',
          items: { type: 'string' },
          description: 'Specific outputs to return',
        },
        stream: {
          type: 'boolean',
          description: 'Enable streaming response (default: false)',
          default: false,
        },
      },
      required: ['flow_id_or_name'],
    },
  },

  {
    name: 'langflow_run_flow_stream',
    description: `Execute a flow with streaming enabled. Returns real-time updates as the flow processes. Use for long-running flows or real-time feedback.`,
    inputSchema: {
      type: 'object',
      properties: {
        flow_id_or_name: {
          type: 'string',
          description: 'Flow ID or endpoint name to execute',
        },
        input_value: {
          type: 'string',
          description: 'Input value for the flow',
        },
        input_type: {
          type: 'string',
          enum: ['chat', 'text', 'json'],
          description: 'Type of input (default: chat)',
          default: 'chat',
        },
        session_id: {
          type: 'string',
          description: 'Session ID for conversation context',
        },
        tweaks: {
          type: 'object',
          description: 'Component parameter overrides',
          additionalProperties: true,
        },
        max_events: {
          type: 'number',
          description: 'Maximum number of stream events to return (default: 100)',
          default: 100,
        },
      },
      required: ['flow_id_or_name'],
    },
  },

  {
    name: 'langflow_trigger_webhook',
    description: `Trigger a flow via webhook. The flow must have a Webhook component configured. Useful for external integrations.`,
    inputSchema: {
      type: 'object',
      properties: {
        flow_id_or_name: {
          type: 'string',
          description: 'Flow ID or endpoint name with webhook',
        },
        data: {
          type: 'object',
          description: 'Data to send to the webhook',
          additionalProperties: true,
        },
        headers: {
          type: 'object',
          description: 'Additional HTTP headers',
          additionalProperties: { type: 'string' },
        },
        wait_for_response: {
          type: 'boolean',
          description: 'Wait for flow completion (default: true)',
          default: true,
        },
      },
      required: ['flow_id_or_name'],
    },
  },

  {
    name: 'langflow_get_execution_status',
    description: `Get the status and results of a flow execution. Use this to check on long-running flows or retrieve results.`,
    inputSchema: {
      type: 'object',
      properties: {
        execution_id: {
          type: 'string',
          description: 'Execution ID to check status for',
        },
        include_outputs: {
          type: 'boolean',
          description: 'Include execution outputs in response (default: true)',
          default: true,
        },
        include_logs: {
          type: 'boolean',
          description: 'Include execution logs (default: false)',
          default: false,
        },
      },
      required: ['execution_id'],
    },
  },

  {
    name: 'langflow_list_executions',
    description: `List recent flow executions with filtering options. Useful for monitoring and debugging.`,
    inputSchema: {
      type: 'object',
      properties: {
        flow_id: {
          type: 'string',
          description: 'Filter by specific flow ID',
        },
        status: {
          type: 'string',
          enum: ['running', 'success', 'error', 'cancelled'],
          description: 'Filter by execution status',
        },
        limit: {
          type: 'number',
          description: 'Maximum number of executions to return (default: 20)',
          default: 20,
        },
        offset: {
          type: 'number',
          description: 'Number of executions to skip (default: 0)',
          default: 0,
        },
        session_id: {
          type: 'string',
          description: 'Filter by session ID',
        },
        start_date: {
          type: 'string',
          description: 'Filter executions after this date (ISO format)',
        },
        end_date: {
          type: 'string',
          description: 'Filter executions before this date (ISO format)',
        },
      },
    },
  },

  {
    name: 'langflow_cancel_execution',
    description: `Cancel a running flow execution. Only works for executions that are currently running.`,
    inputSchema: {
      type: 'object',
      properties: {
        execution_id: {
          type: 'string',
          description: 'Execution ID to cancel',
        },
      },
      required: ['execution_id'],
    },
  },

  {
    name: 'langflow_get_flow_sessions',
    description: `Get all sessions for a flow, useful for conversation management and context tracking.`,
    inputSchema: {
      type: 'object',
      properties: {
        flow_id: {
          type: 'string',
          description: 'Flow ID to get sessions for',
        },
        limit: {
          type: 'number',
          description: 'Maximum number of sessions to return (default: 50)',
          default: 50,
        },
        active_only: {
          type: 'boolean',
          description: 'Only return active sessions (default: false)',
          default: false,
        },
      },
      required: ['flow_id'],
    },
  },

  {
    name: 'langflow_clear_session',
    description: `Clear a specific session's conversation history and context. Useful for starting fresh conversations.`,
    inputSchema: {
      type: 'object',
      properties: {
        session_id: {
          type: 'string',
          description: 'Session ID to clear',
        },
        flow_id: {
          type: 'string',
          description: 'Flow ID associated with the session',
        },
      },
      required: ['session_id'],
    },
  },

  {
    name: 'langflow_test_flow',
    description: `Test a flow with sample inputs to verify it works correctly. Returns detailed execution information for debugging.`,
    inputSchema: {
      type: 'object',
      properties: {
        flow_id: {
          type: 'string',
          description: 'Flow ID to test',
        },
        test_inputs: {
          type: 'object',
          description: 'Test input values for flow components',
          additionalProperties: true,
        },
        validate_outputs: {
          type: 'boolean',
          description: 'Validate that outputs match expected types (default: true)',
          default: true,
        },
        include_performance: {
          type: 'boolean',
          description: 'Include performance metrics (default: true)',
          default: true,
        },
      },
      required: ['flow_id'],
    },
  },

  {
    name: 'langflow_get_flow_metrics',
    description: `Get performance and usage metrics for a flow including execution times, success rates, and error patterns.`,
    inputSchema: {
      type: 'object',
      properties: {
        flow_id: {
          type: 'string',
          description: 'Flow ID to get metrics for',
        },
        time_range: {
          type: 'string',
          enum: ['1h', '24h', '7d', '30d'],
          description: 'Time range for metrics (default: 24h)',
          default: '24h',
        },
        include_component_metrics: {
          type: 'boolean',
          description: 'Include per-component performance metrics (default: false)',
          default: false,
        },
      },
      required: ['flow_id'],
    },
  },
];
