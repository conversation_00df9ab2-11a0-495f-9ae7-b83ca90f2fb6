// Export all MCP tools for easy importing
export { flowManagementTools } from './flow_management';
export { componentDiscoveryTools } from './component_discovery';
export { flowExecutionTools } from './flow_execution';

// Combined exports for convenience
import { flowManagementTools } from './flow_management';
import { componentDiscoveryTools } from './component_discovery';
import { flowExecutionTools } from './flow_execution';

export const allTools = [
  ...flowManagementTools,
  ...componentDiscoveryTools,
  ...flowExecutionTools,
];

export const toolsByCategory = {
  flowManagement: flowManagementTools,
  componentDiscovery: componentDiscoveryTools,
  flowExecution: flowExecutionTools,
};

// Tool name mappings for easy lookup
export const toolNames = {
  // Flow Management
  LIST_FLOWS: 'langflow_list_flows',
  GET_FLOW: 'langflow_get_flow',
  CREATE_FLOW: 'langflow_create_flow',
  UPDATE_FLOW: 'langflow_update_flow',
  DELETE_FLOW: 'langflow_delete_flow',
  DUPLICATE_FLOW: 'langflow_duplicate_flow',
  VALIDATE_FLOW: 'langflow_validate_flow',

  // Component Discovery
  LIST_COMPONENTS: 'langflow_list_components',
  SEARCH_COMPONENTS: 'langflow_search_components',
  GET_COMPONENT_DETAILS: 'langflow_get_component_details',
  GET_COMPONENT_CATEGORIES: 'langflow_get_component_categories',
  GET_COMPONENT_INPUTS: 'langflow_get_component_inputs',
  GET_COMPONENT_OUTPUTS: 'langflow_get_component_outputs',
  FIND_COMPATIBLE_COMPONENTS: 'langflow_find_compatible_components',
  GET_COMPONENT_EXAMPLES: 'langflow_get_component_examples',
  VALIDATE_COMPONENT_CONFIG: 'langflow_validate_component_config',
  GET_COMPONENT_DOCUMENTATION: 'langflow_get_component_documentation',

  // Flow Execution
  RUN_FLOW: 'langflow_run_flow',
  RUN_FLOW_STREAM: 'langflow_run_flow_stream',
  TRIGGER_WEBHOOK: 'langflow_trigger_webhook',
  GET_EXECUTION_STATUS: 'langflow_get_execution_status',
  LIST_EXECUTIONS: 'langflow_list_executions',
  CANCEL_EXECUTION: 'langflow_cancel_execution',
  GET_FLOW_SESSIONS: 'langflow_get_flow_sessions',
  CLEAR_SESSION: 'langflow_clear_session',
  TEST_FLOW: 'langflow_test_flow',
  GET_FLOW_METRICS: 'langflow_get_flow_metrics',
} as const;

// Helper function to get tools by category
export function getToolsByCategory(category: keyof typeof toolsByCategory) {
  return toolsByCategory[category];
}

// Helper function to check if a tool requires API access
export function requiresApiAccess(toolName: string): boolean {
  return toolName.startsWith('langflow_') && 
         !toolName.startsWith('langflow_list_components') &&
         !toolName.startsWith('langflow_search_components') &&
         !toolName.startsWith('langflow_get_component');
}

// Helper function to get tool category
export function getToolCategory(toolName: string): string {
  if (toolName.includes('component')) {
    return 'componentDiscovery';
  } else if (toolName.includes('run_') || toolName.includes('execution') || 
             toolName.includes('trigger_') || toolName.includes('session')) {
    return 'flowExecution';
  } else if (toolName.includes('flow')) {
    return 'flowManagement';
  }
  return 'unknown';
}
