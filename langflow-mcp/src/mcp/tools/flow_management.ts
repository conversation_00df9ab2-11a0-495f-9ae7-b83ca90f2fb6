import { ToolDefinition } from '@modelcontextprotocol/sdk/types.js';

/**
 * Langflow Flow Management MCP Tools
 * 
 * These tools enable AI agents to manage Langflow flows through the Langflow API.
 * They require LANGFLOW_API_URL and LANGFLOW_API_KEY to be configured.
 */
export const flowManagementTools: ToolDefinition[] = [
  {
    name: 'langflow_list_flows',
    description: `List all flows in the Langflow instance. Returns flow metadata including ID, name, description, and status. Use for discovering available flows.`,
    inputSchema: {
      type: 'object',
      properties: {
        limit: {
          type: 'number',
          description: 'Maximum number of flows to return (default: 50)',
          default: 50,
        },
        offset: {
          type: 'number',
          description: 'Number of flows to skip for pagination (default: 0)',
          default: 0,
        },
        folder_id: {
          type: 'string',
          description: 'Filter flows by folder ID',
        },
        components_only: {
          type: 'boolean',
          description: 'Only return flows marked as components',
          default: false,
        },
        header_flows: {
          type: 'boolean',
          description: 'Return minimal flow headers only',
          default: false,
        },
      },
    },
  },

  {
    name: 'langflow_get_flow',
    description: `Get detailed information about a specific flow including nodes, edges, and configuration. Use this to examine flow structure before modification.`,
    inputSchema: {
      type: 'object',
      properties: {
        flow_id: {
          type: 'string',
          description: 'The ID of the flow to retrieve',
        },
      },
      required: ['flow_id'],
    },
  },

  {
    name: 'langflow_create_flow',
    description: `Create a new flow in Langflow. Requires flow name, nodes array, and edges array. Returns the created flow with assigned ID.`,
    inputSchema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          description: 'Name of the new flow',
        },
        description: {
          type: 'string',
          description: 'Optional description of the flow',
        },
        data: {
          type: 'object',
          description: 'Flow data containing nodes and edges',
          properties: {
            nodes: {
              type: 'array',
              description: 'Array of flow nodes',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  type: { type: 'string' },
                  position: {
                    type: 'array',
                    items: { type: 'number' },
                    minItems: 2,
                    maxItems: 2,
                  },
                  data: { type: 'object' },
                },
                required: ['id', 'type', 'position', 'data'],
              },
            },
            edges: {
              type: 'array',
              description: 'Array of connections between nodes',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  source: { type: 'string' },
                  target: { type: 'string' },
                  sourceHandle: { type: 'string' },
                  targetHandle: { type: 'string' },
                },
                required: ['id', 'source', 'target'],
              },
            },
            viewport: {
              type: 'object',
              description: 'Optional viewport settings',
              properties: {
                x: { type: 'number' },
                y: { type: 'number' },
                zoom: { type: 'number' },
              },
            },
          },
          required: ['nodes', 'edges'],
        },
        is_component: {
          type: 'boolean',
          description: 'Mark flow as a reusable component',
          default: false,
        },
        folder_id: {
          type: 'string',
          description: 'Optional folder ID to organize the flow',
        },
        endpoint_name: {
          type: 'string',
          description: 'Optional custom endpoint name for API access',
        },
        tags: {
          type: 'array',
          items: { type: 'string' },
          description: 'Optional tags for categorizing the flow',
        },
        webhook: {
          type: 'boolean',
          description: 'Enable webhook trigger for this flow',
          default: false,
        },
      },
      required: ['name', 'data'],
    },
  },

  {
    name: 'langflow_update_flow',
    description: `Update an existing flow. Can modify name, description, nodes, edges, or other properties. Use langflow_get_flow first to get current state.`,
    inputSchema: {
      type: 'object',
      properties: {
        flow_id: {
          type: 'string',
          description: 'ID of the flow to update',
        },
        name: {
          type: 'string',
          description: 'New name for the flow',
        },
        description: {
          type: 'string',
          description: 'New description for the flow',
        },
        data: {
          type: 'object',
          description: 'Updated flow data with nodes and edges',
          properties: {
            nodes: { type: 'array' },
            edges: { type: 'array' },
            viewport: { type: 'object' },
          },
        },
        is_component: {
          type: 'boolean',
          description: 'Update component status',
        },
        folder_id: {
          type: 'string',
          description: 'Move to different folder',
        },
        endpoint_name: {
          type: 'string',
          description: 'Update custom endpoint name',
        },
        tags: {
          type: 'array',
          items: { type: 'string' },
          description: 'Update flow tags',
        },
        webhook: {
          type: 'boolean',
          description: 'Enable/disable webhook trigger',
        },
      },
      required: ['flow_id'],
    },
  },

  {
    name: 'langflow_delete_flow',
    description: `Permanently delete a flow. This action cannot be undone. Use with caution.`,
    inputSchema: {
      type: 'object',
      properties: {
        flow_id: {
          type: 'string',
          description: 'ID of the flow to delete',
        },
      },
      required: ['flow_id'],
    },
  },

  {
    name: 'langflow_duplicate_flow',
    description: `Create a copy of an existing flow with a new name. Useful for creating variations or backups.`,
    inputSchema: {
      type: 'object',
      properties: {
        flow_id: {
          type: 'string',
          description: 'ID of the flow to duplicate',
        },
        new_name: {
          type: 'string',
          description: 'Name for the duplicated flow (optional, defaults to "Original Name (Copy)")',
        },
      },
      required: ['flow_id'],
    },
  },

  {
    name: 'langflow_validate_flow',
    description: `Validate a flow configuration before creation or update. Checks for errors in node connections, required fields, and data types.`,
    inputSchema: {
      type: 'object',
      properties: {
        flow_data: {
          type: 'object',
          description: 'Flow data to validate (nodes and edges)',
          properties: {
            nodes: { type: 'array' },
            edges: { type: 'array' },
          },
          required: ['nodes', 'edges'],
        },
      },
      required: ['flow_data'],
    },
  },
];
