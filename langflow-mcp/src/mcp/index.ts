#!/usr/bin/env node

import { LangflowMCPServer } from './server';
import { logger } from '../utils/logger';
import { getConfig } from '../utils/config';

// Add error details to stderr for debugging
process.on('uncaughtException', (error) => {
  const config = getConfig();
  if (config.mode !== 'stdio') {
    console.error('Uncaught Exception:', error);
  }
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  const config = getConfig();
  if (config.mode !== 'stdio') {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  }
  logger.error('Unhandled Rejection:', reason);
  process.exit(1);
});

async function main() {
  const config = getConfig();
  
  try {
    // Only show debug messages in HTTP mode to avoid corrupting stdio communication
    if (config.mode === 'http') {
      console.error(`Starting Langflow MCP Server in ${config.mode} mode...`);
      console.error('Current directory:', process.cwd());
      console.error('Node version:', process.version);
    }

    if (config.mode === 'http') {
      // HTTP mode - for remote deployment
      const { startHTTPServer } = await import('../http-server');
      await startHTTPServer();
    } else {
      // Stdio mode - for local Claude Desktop
      const server = new LangflowMCPServer();
      await server.run();
    }
  } catch (error) {
    // In stdio mode, we cannot output to console at all
    if (config.mode !== 'stdio') {
      console.error('Failed to start MCP server:', error);
      logger.error('Failed to start MCP server', error);
      
      // Provide helpful error messages
      if (error instanceof Error) {
        if (error.message.includes('Langflow API')) {
          console.error('\nTo fix this issue:');
          console.error('1. Ensure Langflow is running and accessible');
          console.error('2. Set LANGFLOW_API_URL to your Langflow instance URL');
          console.error('3. Set LANGFLOW_API_KEY to a valid API key');
          console.error('4. Check network connectivity to Langflow');
        }
      }
    }
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
