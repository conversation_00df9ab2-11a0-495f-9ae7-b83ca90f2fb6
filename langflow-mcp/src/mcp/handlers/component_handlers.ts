import { LangflowApiClient } from '../../services/langflow_api';
import { Component, ComponentCategory, ComponentSearchResult } from '../../models/component';
import { logger } from '../../utils/logger';

export class ComponentDiscoveryHandlers {
  private componentCache: Record<string, Record<string, Component>> = {};
  private cacheTimestamp: number = 0;
  private readonly CACHE_TTL = 3600000; // 1 hour in milliseconds

  constructor(private apiClient: LangflowApiClient) {}

  async initializeComponentCache(): Promise<void> {
    try {
      logger.info('Initializing component cache...');
      await this.refreshComponentCache();
      logger.info(`Component cache initialized with ${this.getTotalComponentCount()} components`);
    } catch (error) {
      logger.error('Failed to initialize component cache:', error);
      throw error;
    }
  }

  private async refreshComponentCache(): Promise<void> {
    try {
      this.componentCache = await this.apiClient.getAllComponents();
      this.cacheTimestamp = Date.now();
    } catch (error) {
      logger.error('Failed to refresh component cache:', error);
      throw error;
    }
  }

  private async ensureCacheValid(): Promise<void> {
    const now = Date.now();
    if (now - this.cacheTimestamp > this.CACHE_TTL) {
      logger.debug('Component cache expired, refreshing...');
      await this.refreshComponentCache();
    }
  }

  private getTotalComponentCount(): number {
    return Object.values(this.componentCache).reduce(
      (total, category) => total + Object.keys(category).length,
      0
    );
  }

  async handleTool(toolName: string, args: any): Promise<any> {
    await this.ensureCacheValid();

    switch (toolName) {
      case 'langflow_list_components':
        return this.handleListComponents(args);
      case 'langflow_search_components':
        return this.handleSearchComponents(args);
      case 'langflow_get_component_details':
        return this.handleGetComponentDetails(args);
      case 'langflow_get_component_categories':
        return this.handleGetComponentCategories(args);
      case 'langflow_get_component_inputs':
        return this.handleGetComponentInputs(args);
      case 'langflow_get_component_outputs':
        return this.handleGetComponentOutputs(args);
      case 'langflow_find_compatible_components':
        return this.handleFindCompatibleComponents(args);
      case 'langflow_get_component_examples':
        return this.handleGetComponentExamples(args);
      case 'langflow_validate_component_config':
        return this.handleValidateComponentConfig(args);
      case 'langflow_get_component_documentation':
        return this.handleGetComponentDocumentation(args);
      default:
        throw new Error(`Unknown component discovery tool: ${toolName}`);
    }
  }

  private async handleListComponents(args: {
    category?: string;
    include_custom?: boolean;
    include_beta?: boolean;
  }): Promise<any> {
    try {
      let categories = this.componentCache;

      // Filter by category if specified
      if (args.category) {
        const categoryData = categories[args.category];
        if (!categoryData) {
          throw new Error(`Category "${args.category}" not found`);
        }
        categories = { [args.category]: categoryData };
      }

      const result: Record<string, any[]> = {};
      let totalComponents = 0;

      for (const [categoryName, components] of Object.entries(categories)) {
        const filteredComponents = Object.values(components).filter(component => {
          // Filter beta components
          if (!args.include_beta && component.beta) {
            return false;
          }
          
          // Filter custom components
          if (!args.include_custom && component.custom) {
            return false;
          }
          
          return true;
        });

        result[categoryName] = filteredComponents.map(component => ({
          name: component.name,
          display_name: component.display_name,
          description: component.description,
          icon: component.icon,
          is_input: component.is_input,
          is_output: component.is_output,
          beta: component.beta,
          custom: component.custom,
          base_classes: component.base_classes,
        }));

        totalComponents += filteredComponents.length;
      }

      return {
        categories: result,
        total_components: totalComponents,
        category_count: Object.keys(result).length,
        filters_applied: {
          category: args.category || 'all',
          include_beta: args.include_beta || false,
          include_custom: args.include_custom !== false,
        },
      };
    } catch (error) {
      logger.error('Error listing components:', error);
      throw new Error(`Failed to list components: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleSearchComponents(args: {
    query: string;
    limit?: number;
    category?: string;
    include_documentation?: boolean;
  }): Promise<any> {
    try {
      const query = args.query.toLowerCase();
      const limit = args.limit || 20;
      const results: ComponentSearchResult[] = [];

      for (const [categoryName, components] of Object.entries(this.componentCache)) {
        // Skip category if filter specified
        if (args.category && categoryName !== args.category) {
          continue;
        }

        for (const component of Object.values(components)) {
          const relevance = this.calculateRelevance(component, query, args.include_documentation);
          
          if (relevance > 0) {
            results.push({
              component,
              relevance,
              matches: this.getMatches(component, query, args.include_documentation),
            });
          }
        }
      }

      // Sort by relevance and limit results
      results.sort((a, b) => b.relevance - a.relevance);
      const limitedResults = results.slice(0, limit);

      return {
        query: args.query,
        results: limitedResults.map(result => ({
          name: result.component.name,
          display_name: result.component.display_name,
          description: result.component.description,
          category: this.findComponentCategory(result.component.name),
          relevance: result.relevance,
          matches: result.matches,
          icon: result.component.icon,
          base_classes: result.component.base_classes,
        })),
        total_matches: results.length,
        showing: limitedResults.length,
        search_info: {
          included_documentation: args.include_documentation !== false,
          category_filter: args.category || 'all',
        },
      };
    } catch (error) {
      logger.error('Error searching components:', error);
      throw new Error(`Failed to search components: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private calculateRelevance(component: Component, query: string, includeDocumentation?: boolean): number {
    let score = 0;
    
    // Exact name match gets highest score
    if (component.name.toLowerCase() === query) {
      score += 100;
    } else if (component.name.toLowerCase().includes(query)) {
      score += 80;
    }
    
    // Display name match
    if (component.display_name.toLowerCase() === query) {
      score += 90;
    } else if (component.display_name.toLowerCase().includes(query)) {
      score += 70;
    }
    
    // Description match
    if (component.description.toLowerCase().includes(query)) {
      score += 50;
    }
    
    // Documentation match (if enabled)
    if (includeDocumentation && component.documentation?.toLowerCase().includes(query)) {
      score += 30;
    }
    
    // Base classes match
    if (component.base_classes.some(cls => cls.toLowerCase().includes(query))) {
      score += 40;
    }
    
    return score;
  }

  private getMatches(component: Component, query: string, includeDocumentation?: boolean) {
    return {
      name: component.name.toLowerCase().includes(query),
      display_name: component.display_name.toLowerCase().includes(query),
      description: component.description.toLowerCase().includes(query),
      documentation: includeDocumentation ? 
        component.documentation?.toLowerCase().includes(query) || false : false,
    };
  }

  private findComponentCategory(componentName: string): string {
    for (const [categoryName, components] of Object.entries(this.componentCache)) {
      if (components[componentName]) {
        return categoryName;
      }
    }
    return 'unknown';
  }

  private async handleGetComponentDetails(args: {
    component_name: string;
    category?: string;
    include_examples?: boolean;
    include_template?: boolean;
  }): Promise<any> {
    try {
      let component: Component | null = null;
      let foundCategory = '';

      // Find component
      if (args.category) {
        const categoryComponents = this.componentCache[args.category];
        if (categoryComponents && categoryComponents[args.component_name]) {
          component = categoryComponents[args.component_name];
          foundCategory = args.category;
        }
      } else {
        // Search all categories
        for (const [categoryName, components] of Object.entries(this.componentCache)) {
          if (components[args.component_name]) {
            component = components[args.component_name];
            foundCategory = categoryName;
            break;
          }
        }
      }

      if (!component) {
        throw new Error(`Component "${args.component_name}" not found${args.category ? ` in category "${args.category}"` : ''}`);
      }

      const result: any = {
        name: component.name,
        display_name: component.display_name,
        description: component.description,
        category: foundCategory,
        icon: component.icon,
        is_input: component.is_input,
        is_output: component.is_output,
        is_composition: component.is_composition,
        beta: component.beta,
        legacy: component.legacy,
        custom: component.custom,
        base_classes: component.base_classes,
        documentation: component.documentation,
      };

      if (args.include_template !== false) {
        result.template = component.template;
      }

      if (args.include_examples !== false) {
        result.examples = this.generateComponentExamples(component);
      }

      return result;
    } catch (error) {
      logger.error('Error getting component details:', error);
      throw new Error(`Failed to get component details: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private generateComponentExamples(component: Component): any[] {
    // Generate basic usage examples based on component type
    const examples = [];
    
    if (component.is_input) {
      examples.push({
        title: 'Basic Input Usage',
        description: `Use ${component.display_name} as a flow input`,
        usage: 'Connect this component to provide input to your flow',
      });
    }
    
    if (component.is_output) {
      examples.push({
        title: 'Basic Output Usage',
        description: `Use ${component.display_name} as a flow output`,
        usage: 'Connect other components to this to capture flow results',
      });
    }
    
    return examples;
  }

  private async handleGetComponentCategories(args: {
    include_counts?: boolean;
    include_subcategories?: boolean;
  }): Promise<any> {
    try {
      const categories: Record<string, any> = {};

      for (const [categoryName, components] of Object.entries(this.componentCache)) {
        categories[categoryName] = {
          name: categoryName,
          display_name: this.formatCategoryName(categoryName),
        };

        if (args.include_counts !== false) {
          categories[categoryName].component_count = Object.keys(components).length;
        }

        if (args.include_subcategories !== false) {
          // Group components by subcategory if available
          const subcategories: Record<string, string[]> = {};
          for (const component of Object.values(components)) {
            const subcategory = component.subcategory || 'general';
            if (!subcategories[subcategory]) {
              subcategories[subcategory] = [];
            }
            subcategories[subcategory].push(component.name);
          }
          categories[categoryName].subcategories = subcategories;
        }
      }

      return {
        categories,
        total_categories: Object.keys(categories).length,
        total_components: this.getTotalComponentCount(),
      };
    } catch (error) {
      logger.error('Error getting component categories:', error);
      throw new Error(`Failed to get component categories: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private formatCategoryName(categoryName: string): string {
    return categoryName
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  // Placeholder implementations for remaining handlers
  private async handleGetComponentInputs(args: any): Promise<any> {
    // Implementation for getting component inputs
    return { message: 'Component inputs handler not yet implemented' };
  }

  private async handleGetComponentOutputs(args: any): Promise<any> {
    // Implementation for getting component outputs
    return { message: 'Component outputs handler not yet implemented' };
  }

  private async handleFindCompatibleComponents(args: any): Promise<any> {
    // Implementation for finding compatible components
    return { message: 'Compatible components handler not yet implemented' };
  }

  private async handleGetComponentExamples(args: any): Promise<any> {
    // Implementation for getting component examples
    return { message: 'Component examples handler not yet implemented' };
  }

  private async handleValidateComponentConfig(args: any): Promise<any> {
    // Implementation for validating component config
    return { message: 'Component config validation handler not yet implemented' };
  }

  private async handleGetComponentDocumentation(args: any): Promise<any> {
    // Implementation for getting component documentation
    return { message: 'Component documentation handler not yet implemented' };
  }
}
