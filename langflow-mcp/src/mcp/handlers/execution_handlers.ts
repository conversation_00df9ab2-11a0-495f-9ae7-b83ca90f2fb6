import { LangflowApiClient } from '../../services/langflow_api';
import { FlowExecutionRequest, FlowExecutionResponse } from '../../models/flow';
import { logger } from '../../utils/logger';

export class FlowExecutionHandlers {
  private activeExecutions: Map<string, any> = new Map();

  constructor(private apiClient: LangflowApiClient) {}

  async handleTool(toolName: string, args: any): Promise<any> {
    switch (toolName) {
      case 'langflow_run_flow':
        return this.handleRunFlow(args);
      case 'langflow_run_flow_stream':
        return this.handleRunFlowStream(args);
      case 'langflow_trigger_webhook':
        return this.handleTriggerWebhook(args);
      case 'langflow_get_execution_status':
        return this.handleGetExecutionStatus(args);
      case 'langflow_list_executions':
        return this.handleListExecutions(args);
      case 'langflow_cancel_execution':
        return this.handleCancelExecution(args);
      case 'langflow_get_flow_sessions':
        return this.handleGetFlowSessions(args);
      case 'langflow_clear_session':
        return this.handleClearSession(args);
      case 'langflow_test_flow':
        return this.handleTestFlow(args);
      case 'langflow_get_flow_metrics':
        return this.handleGetFlowMetrics(args);
      default:
        throw new Error(`Unknown flow execution tool: ${toolName}`);
    }
  }

  private async handleRunFlow(args: {
    flow_id_or_name: string;
    input_value?: string;
    input_type?: string;
    output_type?: string;
    output_component?: string;
    session_id?: string;
    tweaks?: Record<string, any>;
    inputs?: Array<{ components: string[]; input_value: string }>;
    outputs?: string[];
    stream?: boolean;
  }): Promise<any> {
    try {
      const executionRequest: FlowExecutionRequest = {
        input_value: args.input_value,
        input_type: args.input_type || 'chat',
        output_type: args.output_type || 'all',
        output_component: args.output_component,
        session_id: args.session_id || this.generateSessionId(),
        tweaks: args.tweaks,
        inputs: args.inputs,
        outputs: args.outputs,
        stream: args.stream || false,
      };

      const startTime = Date.now();
      const response = await this.apiClient.runFlow(args.flow_id_or_name, executionRequest);
      const endTime = Date.now();

      // Store execution info for tracking
      const executionId = this.generateExecutionId();
      this.activeExecutions.set(executionId, {
        id: executionId,
        flow_id: args.flow_id_or_name,
        status: 'success',
        started_at: new Date(startTime).toISOString(),
        ended_at: new Date(endTime).toISOString(),
        duration_ms: endTime - startTime,
        session_id: executionRequest.session_id,
        response,
      });

      return {
        execution_id: executionId,
        session_id: response.session_id,
        status: 'success',
        duration_ms: endTime - startTime,
        outputs: response.outputs,
        metadata: {
          flow_id: args.flow_id_or_name,
          input_type: executionRequest.input_type,
          output_type: executionRequest.output_type,
          stream_enabled: executionRequest.stream || false,
        },
      };
    } catch (error) {
      logger.error('Error running flow:', error);
      
      // Store failed execution info
      const executionId = this.generateExecutionId();
      this.activeExecutions.set(executionId, {
        id: executionId,
        flow_id: args.flow_id_or_name,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        started_at: new Date().toISOString(),
      });

      throw new Error(`Failed to run flow: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleRunFlowStream(args: {
    flow_id_or_name: string;
    input_value?: string;
    input_type?: string;
    session_id?: string;
    tweaks?: Record<string, any>;
    max_events?: number;
  }): Promise<any> {
    try {
      const executionRequest: FlowExecutionRequest = {
        input_value: args.input_value,
        input_type: args.input_type || 'chat',
        session_id: args.session_id || this.generateSessionId(),
        tweaks: args.tweaks,
        stream: true,
      };

      const executionId = this.generateExecutionId();
      const startTime = Date.now();
      
      // Store execution info
      this.activeExecutions.set(executionId, {
        id: executionId,
        flow_id: args.flow_id_or_name,
        status: 'running',
        started_at: new Date(startTime).toISOString(),
        session_id: executionRequest.session_id,
        stream: true,
      });

      const streamResponse = await this.apiClient.runFlowStream(args.flow_id_or_name, executionRequest);
      const events: any[] = [];
      const maxEvents = args.max_events || 100;

      // Collect stream events
      let eventCount = 0;
      for await (const event of streamResponse) {
        if (eventCount >= maxEvents) {
          break;
        }
        events.push({
          timestamp: new Date().toISOString(),
          data: event,
        });
        eventCount++;
      }

      // Update execution status
      const execution = this.activeExecutions.get(executionId);
      if (execution) {
        execution.status = 'success';
        execution.ended_at = new Date().toISOString();
        execution.duration_ms = Date.now() - startTime;
        execution.event_count = events.length;
      }

      return {
        execution_id: executionId,
        session_id: executionRequest.session_id,
        status: 'success',
        stream_events: events,
        event_count: events.length,
        max_events_reached: eventCount >= maxEvents,
        metadata: {
          flow_id: args.flow_id_or_name,
          input_type: executionRequest.input_type,
          streaming: true,
        },
      };
    } catch (error) {
      logger.error('Error running flow stream:', error);
      throw new Error(`Failed to run flow stream: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleTriggerWebhook(args: {
    flow_id_or_name: string;
    data?: any;
    headers?: Record<string, string>;
    wait_for_response?: boolean;
  }): Promise<any> {
    try {
      const response = await this.apiClient.triggerWebhook(
        args.flow_id_or_name,
        args.data || {},
        args.headers
      );

      const executionId = this.generateExecutionId();
      this.activeExecutions.set(executionId, {
        id: executionId,
        flow_id: args.flow_id_or_name,
        status: 'triggered',
        triggered_at: new Date().toISOString(),
        webhook: true,
        response,
      });

      return {
        execution_id: executionId,
        webhook_response: response,
        status: response.status,
        message: response.message,
        metadata: {
          flow_id: args.flow_id_or_name,
          trigger_method: 'webhook',
          wait_for_response: args.wait_for_response !== false,
        },
      };
    } catch (error) {
      logger.error('Error triggering webhook:', error);
      throw new Error(`Failed to trigger webhook: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleGetExecutionStatus(args: {
    execution_id: string;
    include_outputs?: boolean;
    include_logs?: boolean;
  }): Promise<any> {
    try {
      const execution = this.activeExecutions.get(args.execution_id);
      
      if (!execution) {
        throw new Error(`Execution ${args.execution_id} not found`);
      }

      const result: any = {
        execution_id: execution.id,
        flow_id: execution.flow_id,
        status: execution.status,
        started_at: execution.started_at,
        ended_at: execution.ended_at,
        duration_ms: execution.duration_ms,
        session_id: execution.session_id,
      };

      if (execution.error) {
        result.error = execution.error;
      }

      if (args.include_outputs !== false && execution.response) {
        result.outputs = execution.response.outputs;
      }

      if (args.include_logs !== false && execution.logs) {
        result.logs = execution.logs;
      }

      return result;
    } catch (error) {
      logger.error('Error getting execution status:', error);
      throw new Error(`Failed to get execution status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleListExecutions(args: {
    flow_id?: string;
    status?: string;
    limit?: number;
    offset?: number;
    session_id?: string;
    start_date?: string;
    end_date?: string;
  }): Promise<any> {
    try {
      let executions = Array.from(this.activeExecutions.values());

      // Apply filters
      if (args.flow_id) {
        executions = executions.filter(exec => exec.flow_id === args.flow_id);
      }

      if (args.status) {
        executions = executions.filter(exec => exec.status === args.status);
      }

      if (args.session_id) {
        executions = executions.filter(exec => exec.session_id === args.session_id);
      }

      if (args.start_date) {
        const startDate = new Date(args.start_date);
        executions = executions.filter(exec => new Date(exec.started_at) >= startDate);
      }

      if (args.end_date) {
        const endDate = new Date(args.end_date);
        executions = executions.filter(exec => new Date(exec.started_at) <= endDate);
      }

      // Sort by start time (newest first)
      executions.sort((a, b) => new Date(b.started_at).getTime() - new Date(a.started_at).getTime());

      // Apply pagination
      const offset = args.offset || 0;
      const limit = args.limit || 20;
      const paginatedExecutions = executions.slice(offset, offset + limit);

      return {
        executions: paginatedExecutions.map(exec => ({
          execution_id: exec.id,
          flow_id: exec.flow_id,
          status: exec.status,
          started_at: exec.started_at,
          ended_at: exec.ended_at,
          duration_ms: exec.duration_ms,
          session_id: exec.session_id,
          error: exec.error,
        })),
        total_count: executions.length,
        showing: paginatedExecutions.length,
        pagination: {
          offset,
          limit,
          has_more: offset + limit < executions.length,
        },
        filters_applied: {
          flow_id: args.flow_id,
          status: args.status,
          session_id: args.session_id,
          start_date: args.start_date,
          end_date: args.end_date,
        },
      };
    } catch (error) {
      logger.error('Error listing executions:', error);
      throw new Error(`Failed to list executions: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateExecutionId(): string {
    return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Placeholder implementations for remaining handlers
  private async handleCancelExecution(args: any): Promise<any> {
    return { message: 'Cancel execution handler not yet implemented' };
  }

  private async handleGetFlowSessions(args: any): Promise<any> {
    return { message: 'Get flow sessions handler not yet implemented' };
  }

  private async handleClearSession(args: any): Promise<any> {
    return { message: 'Clear session handler not yet implemented' };
  }

  private async handleTestFlow(args: any): Promise<any> {
    return { message: 'Test flow handler not yet implemented' };
  }

  private async handleGetFlowMetrics(args: any): Promise<any> {
    return { message: 'Get flow metrics handler not yet implemented' };
  }
}
