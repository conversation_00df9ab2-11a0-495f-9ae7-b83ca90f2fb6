import { LangflowApiClient } from '../../services/langflow_api';
import { Flow, FlowCreate, FlowUpdate } from '../../models/flow';
import { logger } from '../../utils/logger';

export class FlowManagementHandlers {
  constructor(private apiClient: LangflowApiClient) {}

  async handleTool(toolName: string, args: any): Promise<any> {
    switch (toolName) {
      case 'langflow_list_flows':
        return this.handleListFlows(args);
      case 'langflow_get_flow':
        return this.handleGetFlow(args);
      case 'langflow_create_flow':
        return this.handleCreateFlow(args);
      case 'langflow_update_flow':
        return this.handleUpdateFlow(args);
      case 'langflow_delete_flow':
        return this.handleDeleteFlow(args);
      case 'langflow_duplicate_flow':
        return this.handleDuplicateFlow(args);
      case 'langflow_validate_flow':
        return this.handleValidateFlow(args);
      default:
        throw new Error(`Unknown flow management tool: ${toolName}`);
    }
  }

  private async handleListFlows(args: {
    limit?: number;
    offset?: number;
    folder_id?: string;
    components_only?: boolean;
    header_flows?: boolean;
  }): Promise<any> {
    try {
      const flows = await this.apiClient.listFlows({
        limit: args.limit || 50,
        offset: args.offset || 0,
        folder_id: args.folder_id,
        components_only: args.components_only || false,
        header_flows: args.header_flows || false,
      });

      return {
        flows: flows.map(flow => ({
          id: flow.id,
          name: flow.name,
          description: flow.description,
          is_component: flow.is_component,
          updated_at: flow.updated_at,
          folder_id: flow.folder_id,
          endpoint_name: flow.endpoint_name,
          tags: flow.tags,
          webhook: flow.webhook,
          status: flow.status,
        })),
        total_count: flows.length,
        has_more: flows.length === (args.limit || 50),
      };
    } catch (error) {
      logger.error('Error listing flows:', error);
      throw new Error(`Failed to list flows: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleGetFlow(args: { flow_id: string }): Promise<any> {
    try {
      const flow = await this.apiClient.getFlow(args.flow_id);
      
      return {
        flow: {
          id: flow.id,
          name: flow.name,
          description: flow.description,
          data: flow.data,
          is_component: flow.is_component,
          updated_at: flow.updated_at,
          folder_id: flow.folder_id,
          endpoint_name: flow.endpoint_name,
          tags: flow.tags,
          webhook: flow.webhook,
          status: flow.status,
        },
        metadata: {
          node_count: flow.data.nodes.length,
          edge_count: flow.data.edges.length,
          component_types: [...new Set(flow.data.nodes.map(node => node.type))],
        },
      };
    } catch (error) {
      logger.error('Error getting flow:', error);
      throw new Error(`Failed to get flow: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleCreateFlow(args: {
    name: string;
    description?: string;
    data: any;
    is_component?: boolean;
    folder_id?: string;
    endpoint_name?: string;
    tags?: string[];
    webhook?: boolean;
  }): Promise<any> {
    try {
      // Validate flow data
      if (!args.data || !args.data.nodes || !args.data.edges) {
        throw new Error('Flow data must include nodes and edges arrays');
      }

      if (!Array.isArray(args.data.nodes) || !Array.isArray(args.data.edges)) {
        throw new Error('Nodes and edges must be arrays');
      }

      // Validate nodes have required fields
      for (const node of args.data.nodes) {
        if (!node.id || !node.type || !node.position || !node.data) {
          throw new Error('Each node must have id, type, position, and data fields');
        }
      }

      // Validate edges have required fields
      for (const edge of args.data.edges) {
        if (!edge.id || !edge.source || !edge.target) {
          throw new Error('Each edge must have id, source, and target fields');
        }
      }

      const flowCreate: FlowCreate = {
        name: args.name,
        description: args.description,
        data: args.data,
        is_component: args.is_component || false,
        folder_id: args.folder_id,
        endpoint_name: args.endpoint_name,
        tags: args.tags || [],
        webhook: args.webhook || false,
      };

      const createdFlow = await this.apiClient.createFlow(flowCreate);

      return {
        success: true,
        flow: {
          id: createdFlow.id,
          name: createdFlow.name,
          description: createdFlow.description,
          endpoint_name: createdFlow.endpoint_name,
          webhook: createdFlow.webhook,
        },
        message: `Flow "${createdFlow.name}" created successfully`,
        api_url: createdFlow.endpoint_name 
          ? `${this.apiClient.getBaseUrl()}/api/v1/run/${createdFlow.endpoint_name}`
          : `${this.apiClient.getBaseUrl()}/api/v1/run/${createdFlow.id}`,
      };
    } catch (error) {
      logger.error('Error creating flow:', error);
      throw new Error(`Failed to create flow: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleUpdateFlow(args: {
    flow_id: string;
    name?: string;
    description?: string;
    data?: any;
    is_component?: boolean;
    folder_id?: string;
    endpoint_name?: string;
    tags?: string[];
    webhook?: boolean;
  }): Promise<any> {
    try {
      // Get current flow first
      const currentFlow = await this.apiClient.getFlow(args.flow_id);

      const flowUpdate: FlowUpdate = {
        name: args.name,
        description: args.description,
        data: args.data,
        is_component: args.is_component,
        folder_id: args.folder_id,
        endpoint_name: args.endpoint_name,
        tags: args.tags,
        webhook: args.webhook,
      };

      // Remove undefined values
      Object.keys(flowUpdate).forEach(key => {
        if (flowUpdate[key as keyof FlowUpdate] === undefined) {
          delete flowUpdate[key as keyof FlowUpdate];
        }
      });

      const updatedFlow = await this.apiClient.updateFlow(args.flow_id, flowUpdate);

      return {
        success: true,
        flow: {
          id: updatedFlow.id,
          name: updatedFlow.name,
          description: updatedFlow.description,
          endpoint_name: updatedFlow.endpoint_name,
          webhook: updatedFlow.webhook,
        },
        changes: this.getFlowChanges(currentFlow, updatedFlow),
        message: `Flow "${updatedFlow.name}" updated successfully`,
      };
    } catch (error) {
      logger.error('Error updating flow:', error);
      throw new Error(`Failed to update flow: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleDeleteFlow(args: { flow_id: string }): Promise<any> {
    try {
      // Get flow info before deletion
      const flow = await this.apiClient.getFlow(args.flow_id);
      
      await this.apiClient.deleteFlow(args.flow_id);

      return {
        success: true,
        message: `Flow "${flow.name}" deleted successfully`,
        deleted_flow: {
          id: flow.id,
          name: flow.name,
        },
      };
    } catch (error) {
      logger.error('Error deleting flow:', error);
      throw new Error(`Failed to delete flow: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleDuplicateFlow(args: {
    flow_id: string;
    new_name?: string;
  }): Promise<any> {
    try {
      const duplicatedFlow = await this.apiClient.duplicateFlow(args.flow_id, args.new_name);

      return {
        success: true,
        original_flow_id: args.flow_id,
        duplicated_flow: {
          id: duplicatedFlow.id,
          name: duplicatedFlow.name,
          description: duplicatedFlow.description,
        },
        message: `Flow duplicated successfully as "${duplicatedFlow.name}"`,
      };
    } catch (error) {
      logger.error('Error duplicating flow:', error);
      throw new Error(`Failed to duplicate flow: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleValidateFlow(args: { flow_data: any }): Promise<any> {
    try {
      const validation = await this.apiClient.validateFlow(args.flow_data);

      return {
        valid: validation.valid,
        errors: validation.errors,
        summary: {
          node_count: args.flow_data.nodes?.length || 0,
          edge_count: args.flow_data.edges?.length || 0,
          error_count: validation.errors.length,
        },
        message: validation.valid 
          ? 'Flow validation passed' 
          : `Flow validation failed with ${validation.errors.length} errors`,
      };
    } catch (error) {
      logger.error('Error validating flow:', error);
      throw new Error(`Failed to validate flow: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private getFlowChanges(oldFlow: Flow, newFlow: Flow): string[] {
    const changes: string[] = [];
    
    if (oldFlow.name !== newFlow.name) {
      changes.push(`Name changed from "${oldFlow.name}" to "${newFlow.name}"`);
    }
    
    if (oldFlow.description !== newFlow.description) {
      changes.push('Description updated');
    }
    
    if (oldFlow.endpoint_name !== newFlow.endpoint_name) {
      changes.push('Endpoint name updated');
    }
    
    if (oldFlow.webhook !== newFlow.webhook) {
      changes.push(`Webhook ${newFlow.webhook ? 'enabled' : 'disabled'}`);
    }
    
    if (oldFlow.data.nodes.length !== newFlow.data.nodes.length) {
      changes.push(`Node count changed from ${oldFlow.data.nodes.length} to ${newFlow.data.nodes.length}`);
    }
    
    if (oldFlow.data.edges.length !== newFlow.data.edges.length) {
      changes.push(`Edge count changed from ${oldFlow.data.edges.length} to ${newFlow.data.edges.length}`);
    }
    
    return changes;
  }
}
