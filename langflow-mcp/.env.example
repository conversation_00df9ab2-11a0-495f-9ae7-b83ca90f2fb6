# Langflow MCP Server Configuration

# MCP Server Mode
LANGFLOW_MCP_MODE=stdio  # stdio (for <PERSON>) or http (for remote access)
LOG_LEVEL=error          # error, warn, info, debug
DISABLE_CONSOLE_OUTPUT=true  # Disable console output in stdio mode

# Langflow API Configuration (Required for flow management)
LANGFLOW_API_URL=http://localhost:7860  # Your Langflow instance URL
LANGFLOW_API_KEY=your-api-key-here      # Your Langflow API key

# Optional: Database Configuration
LANGFLOW_MCP_DB_PATH=./data/components.db  # Path to component cache database

# Optional: Component Cache Settings
COMPONENT_CACHE_TTL=3600  # Component cache TTL in seconds (1 hour)
AUTO_UPDATE_COMPONENTS=true  # Auto-update component cache

# Optional: Flow Execution Settings
DEFAULT_SESSION_TIMEOUT=300  # Default session timeout in seconds
MAX_CONCURRENT_EXECUTIONS=5  # Maximum concurrent flow executions

# Optional: Template Settings
TEMPLATES_PATH=./data/templates  # Path to flow templates directory

# Optional: HTTP Server Settings (only for http mode)
HTTP_PORT=3000
HTTP_HOST=localhost
