import os
import shutil
from datetime import datetime

def organize_screenshots(desktop_path):
    # Create a directory to store organized screenshots
    organized_dir = os.path.join(desktop_path, 'Screenshots_Organized')
    os.makedirs(organized_dir, exist_ok=True)

    for filename in os.listdir(desktop_path):
        if filename.startswith('Screenshot') and filename.endswith('.png'):
            # Extract date from filename
            date_str = filename.replace('Screenshot', '').replace('.png', '').strip()
            date = datetime.strptime(date_str, '%Y-%m-%d at %H.%M.%S %p')

            # Create a directory for the year and month
            year_dir = os.path.join(organized_dir, str(date.year))
            month_dir = os.path.join(year_dir, date.strftime('%B'))
            os.makedirs(year_dir, exist_ok=True)
            os.makedirs(month_dir, exist_ok=True)

            # Rename and move the file
            new_filename = f'Screenshot_{date.strftime("%Y-%m-%d_%H-%M-%S")}.png'
            new_path = os.path.join(month_dir, new_filename)
            shutil.move(os.path.join(desktop_path, filename), new_path)
            print(f'Moved {filename} to {new_path}')

if __name__ == '__main__':
    desktop_path = os.path.expanduser('~/Desktop')
    organize_screenshots(desktop_path)
