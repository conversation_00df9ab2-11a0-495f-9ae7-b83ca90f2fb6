"""
Advanced Convex Schema Component using Pica OS Tools
This component provides schema management and data operations for Convex databases.
"""

import json
from typing import Any, Dict, List, Optional

import httpx
from loguru import logger

from langflow.custom.custom_component.component import Component
from langflow.io import (
    BoolInput,
    DropdownInput,
    MessageTextInput,
    MultilineInput,
    Output,
    SecretStrInput,
    StrInput,
)
from langflow.schema.data import Data
from langflow.schema.message import Message


class ConvexSchemaComponent(Component):
    """
    Advanced Convex component for schema management and data operations using Pica OS.
    """

    display_name = "Convex Schema Manager (Pica)"
    description = "Manage Convex database schemas, tables, and perform advanced data operations"
    documentation = "https://docs.convex.dev/database/schemas"
    icon = "database"
    name = "ConvexSchemaManager"

    inputs = [
        # Pica Configuration
        SecretStrInput(
            name="pica_api_key",
            display_name="Pica API Key",
            info="Your Pica OS API key for authentication",
            required=True,
        ),
        StrInput(
            name="pica_connection_key",
            display_name="Pica Connection Key",
            info="Your Pica Convex connection key",
            required=True,
        ),
        
        # Schema Operations
        DropdownInput(
            name="schema_operation",
            display_name="Schema Operation",
            options=[
                "list_schemas",
                "get_document_deltas", 
                "list_snapshot",
                "clear_tables",
                "add_indexes",
                "check_indexes"
            ],
            value="list_schemas",
            info="Type of schema operation to perform",
        ),
        
        # Table Management
        MessageTextInput(
            name="table_names",
            display_name="Table Names",
            info="Comma-separated list of table names (for operations that require it)",
            placeholder="messages,users,channels",
        ),
        
        # Advanced Options
        BoolInput(
            name="include_metadata",
            display_name="Include Metadata",
            info="Include metadata fields (_ts, _deleted, _table) in schema responses",
            value=False,
        ),
        BoolInput(
            name="delta_schema",
            display_name="Delta Schema",
            info="Include delta schema information for document tracking",
            value=False,
        ),
        
        # Custom Parameters
        MultilineInput(
            name="custom_params",
            display_name="Custom Parameters",
            info="Additional parameters as JSON for specific operations",
            placeholder='{"format": "json", "limit": 100}',
            value="{}",
        ),
    ]

    outputs = [
        Output(display_name="Schema Data", name="schema_data", method="execute_schema_operation"),
        Output(display_name="Table Info", name="table_info", method="get_table_info"),
        Output(display_name="Operation Status", name="status", method="get_operation_status"),
    ]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._last_response = None
        self._operation_status = None

    def _get_schema_action_mapping(self) -> Dict[str, Dict[str, str]]:
        """Get mapping of schema operations to Pica action IDs and paths."""
        return {
            "list_schemas": {
                "action_id": "conn_mod_def::GC-4oS4eIMM::sEFk_IffQZaRB31dZEoAZw",
                "path": "/api/json_schemas",
                "method": "GET"
            },
            "get_document_deltas": {
                "action_id": "conn_mod_def::GC-4oOyzFhM::jLbwCzEXR9ysNbTB7pVdHA", 
                "path": "/api/document_deltas",
                "method": "GET"
            },
            "list_snapshot": {
                "action_id": "conn_mod_def::GC-4pWX8h3I::IayEs6b7RVaVD9vGkwBkIQ",
                "path": "/api/list_snapshot", 
                "method": "GET"
            },
            "clear_tables": {
                "action_id": "conn_mod_def::GC-4otOoUS0::wUCo9dzcTCaA682sPHJiXw",
                "path": "/api/streaming_import/clear_tables",
                "method": "PUT"
            },
            "add_indexes": {
                "action_id": "conn_mod_def::GC-4ovl2KYg::A38EPWBDTrOuqwVuOu9XVA",
                "path": "/api/add_primary_key_indexes",
                "method": "POST"
            },
            "check_indexes": {
                "action_id": "conn_mod_def::GC-4olefvxs::zsSz7bmfRh-daJ9f5QLjeg",
                "path": "/api/check_primary_key_indexes_readiness",
                "method": "GET"
            }
        }

    async def _execute_pica_request(self, operation_config: Dict[str, str], params: Dict[str, Any]) -> Dict:
        """Execute a Pica request for schema operations."""
        
        pica_url = "https://api.pica.ai/v1/execute"
        
        headers = {
            "Authorization": f"Bearer {self.pica_api_key}",
            "Content-Type": "application/json",
        }
        
        payload = {
            "actionId": operation_config["action_id"],
            "connectionKey": self.pica_connection_key,
            "method": operation_config["method"],
            "path": operation_config["path"],
        }
        
        # Add parameters based on HTTP method
        if operation_config["method"] in ["GET"]:
            payload["queryParams"] = params
        else:
            payload["data"] = params
            
        logger.info(f"Executing schema operation: {self.schema_operation}")
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(pica_url, headers=headers, json=payload, timeout=60.0)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                logger.error(f"Schema operation failed: {e}")
                raise ValueError(f"Failed to execute schema operation: {e}")

    def _prepare_operation_params(self) -> Dict[str, Any]:
        """Prepare parameters for the schema operation."""
        params = {}
        
        # Parse custom parameters
        try:
            if self.custom_params and self.custom_params.strip():
                custom = json.loads(self.custom_params)
                params.update(custom)
        except json.JSONDecodeError:
            logger.warning("Invalid JSON in custom_params, ignoring")
        
        # Add operation-specific parameters
        if self.schema_operation in ["list_schemas"]:
            params["deltaSchema"] = self.delta_schema
            params["format"] = "json"
            
        elif self.schema_operation == "clear_tables":
            if self.table_names:
                table_list = [name.strip() for name in self.table_names.split(",")]
                params["tableNames"] = table_list
            else:
                raise ValueError("Table names are required for clear_tables operation")
                
        return params

    async def execute_schema_operation(self) -> Data:
        """Execute the selected schema operation."""
        try:
            # Get operation configuration
            action_mapping = self._get_schema_action_mapping()
            if self.schema_operation not in action_mapping:
                raise ValueError(f"Unsupported schema operation: {self.schema_operation}")
            
            operation_config = action_mapping[self.schema_operation]
            params = self._prepare_operation_params()
            
            # Execute the operation
            response = await self._execute_pica_request(operation_config, params)
            self._last_response = response
            
            # Process response
            if response.get("success", True):  # Some operations may not have explicit success field
                result_data = {
                    "operation": self.schema_operation,
                    "success": True,
                    "data": response.get("data", response),
                    "timestamp": response.get("timestamp"),
                }
                
                self._operation_status = "success"
                return Data(data=result_data, value=response.get("data", response))
            else:
                error_data = {
                    "operation": self.schema_operation,
                    "success": False,
                    "error": response.get("error", "Unknown error"),
                }
                self._operation_status = "error"
                return Data(data=error_data, value=None)
                
        except Exception as e:
            logger.error(f"Schema operation failed: {e}")
            self._operation_status = "error"
            error_data = {
                "operation": self.schema_operation,
                "success": False,
                "error": str(e),
            }
            return Data(data=error_data, value=None)

    def get_table_info(self) -> Message:
        """Extract and format table information from the last response."""
        if not self._last_response:
            return Message(text="No schema operation has been executed yet.")
        
        if self.schema_operation == "list_schemas":
            # Format schema information
            schemas = self._last_response.get("data", {})
            table_info = []
            
            for table_name, schema in schemas.items():
                if isinstance(schema, dict):
                    properties = schema.get("properties", {})
                    table_info.append(f"Table: {table_name}")
                    table_info.append(f"  Fields: {len(properties)}")
                    for field_name, field_schema in properties.items():
                        field_type = field_schema.get("type", "unknown")
                        table_info.append(f"    - {field_name}: {field_type}")
                    table_info.append("")
            
            return Message(text="\n".join(table_info) if table_info else "No table information available")
        
        return Message(text=json.dumps(self._last_response, indent=2))

    def get_operation_status(self) -> Message:
        """Return the status of the last operation."""
        if self._operation_status is None:
            return Message(text="No operation has been executed yet.")
        
        status_info = {
            "status": self._operation_status,
            "operation": self.schema_operation,
            "timestamp": self._last_response.get("timestamp") if self._last_response else None,
        }
        
        return Message(text=json.dumps(status_info, indent=2))
