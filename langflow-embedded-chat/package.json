{"name": "langflow-embedded-chat", "version": "0.1.0", "private": true, "dependencies": {"@r2wc/react-to-web-component": "^2.0.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.37", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "axios": "^1.4.0", "lucide-react": "^0.256.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^8.0.7", "react-scripts": "5.0.1", "react-shadow": "^20.3.0", "rehype-mathjax": "^4.0.3", "remark-gfm": "3.0.1", "typescript": "^4.9.5", "uglifyjs-webpack-plugin": "^2.2.0", "uuid": "^10.0.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "npm install --legacy-peer-deps && npm run build:react && npm run build:bundle", "build:react": "react-scripts build", "build:bundle": "webpack --config webpack.config.js", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"webpack-cli": "^5.1.4"}}