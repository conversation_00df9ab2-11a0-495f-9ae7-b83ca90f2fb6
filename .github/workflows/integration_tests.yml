name: Integration Tests

on:
  workflow_dispatch:
    inputs:
      ref:
        description: "(Optional) ref to checkout"
        required: false
        type: string
  workflow_call:
    inputs:
      python-versions:
        description: "(Optional) Python versions to test"
        required: true
        type: string
        default: "['3.10', '3.11', '3.12', '3.13']"
      ref:
        description: "(Optional) ref to checkout"
        required: false
        type: string

env:
  POETRY_VERSION: "1.8.2"

jobs:
  integration-tests:
    name: Run Integration Tests
    runs-on: ubuntu-latest
    strategy:
      max-parallel: 1 # Currently, we can only run at a time for collection-per-db-constraints
      matrix:
        python-version:
          - "3.13"
          - "3.12"
          - "3.11"
          - "3.10"
    env:
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      ASTRA_DB_API_ENDPOINT: ${{ secrets.ASTRA_DB_API_ENDPOINT }}
      ASTRA_DB_APPLICATION_TOKEN: ${{ secrets.ASTRA_DB_APPLICATION_TOKEN }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref || github.ref }}
      - name: "Setup Environment"
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true
          cache-dependency-glob: "uv.lock"
          python-version: ${{ matrix.python-version }}
          prune-cache: false
      - name: Run integration tests with api keys
        timeout-minutes: 20
        run: |
          make integration_tests_api_keys
