name: Test Docs Build

on:
  workflow_call:
  workflow_dispatch:
    inputs:
      branch:
        description: "(Optional) Branch to checkout"
        required: false
        type: string

env:
  NODE_VERSION: "21"

jobs:
  test-docs-build:
    name: Test Docs Build
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.branch || github.ref }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        id: setup-node
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: yarn
          cache-dependency-path: ./docs/yarn.lock

      - name: Install dependencies
        run: cd docs && yarn install --frozen-lockfile

      - name: Build docs
        run: cd docs && yarn build
