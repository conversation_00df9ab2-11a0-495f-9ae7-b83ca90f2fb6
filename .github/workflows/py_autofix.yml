name: autofix.ci
on:
  pull_request:
    paths:
      - "**/*.py"
env:
  PYTHON_VERSION: "3.13"


jobs:
  lint:
    name: Run Ruff Check and Format
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: "Setup Environment"
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true
          cache-dependency-glob: "uv.lock"
          python-version: ${{ env.PYTHON_VERSION }}
          prune-cache: false
      - run: uv run ruff check --fix-only .
      - run: uv run ruff format . --config pyproject.toml
      - uses: autofix-ci/action@551dded8c6cc8a1054039c8bc0b8b48c51dfc6ef


  update-starter-projects:
    name: Update Starter Projects
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: "Setup Environment"
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true
          cache-dependency-glob: "uv.lock"
          python-version: ${{ env.PYTHON_VERSION }}
          prune-cache: false

      - name: "Install dependencies"
        run: |
          uv sync
          uv pip install -e .

      - name: Run starter projects update
        run: uv run python scripts/ci/update_starter_projects.py

      - uses: autofix-ci/action@551dded8c6cc8a1054039c8bc0b8b48c51dfc6ef



