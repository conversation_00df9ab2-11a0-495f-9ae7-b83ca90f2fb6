name: <PERSON><PERSON>

on:
  workflow_call:
  workflow_dispatch:
    inputs:
      branch:
        description: "(Optional) Branch to checkout"
        required: false
        type: string
env:
  POETRY_VERSION: "1.8.2"


jobs:
  lint:
    name: Run Mypy
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version:
          - "3.13"
          - "3.12"
          - "3.11"
          - "3.10"
    steps:
      - name: Check out the code at a specific ref
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.branch || github.ref }}
          persist-credentials: true
      - name: "Setup Environment"
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true
          cache-dependency-glob: "uv.lock"
          python-version: ${{ matrix.python-version }}
          prune-cache: false
      - name: Install the project
        run: uv sync
      - name: Run Mypy
        run: |
          uv run mypy --namespace-packages -p "langflow"
        env:
          GITHUB_TOKEN: ${{ secrets.github_token }}

