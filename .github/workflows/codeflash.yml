name: Codeflash

on:
  pull_request:
    paths:
      - "src/backend/base/langflow/**"
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  optimize:
    name: Optimize new Python code in this PR
    if: ${{ github.actor != 'codeflash-ai[bot]' }}
    runs-on: ubuntu-latest
    env:
      CODEFLASH_API_KEY: ${{ secrets.CODEFLASH_API_KEY }}
      CODEFLASH_PR_NUMBER: ${{ github.event.number }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: "Setup Environment"
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true
          cache-dependency-glob: "uv.lock"
          python-version: ${{ matrix.python-version }}
          prune-cache: false
      - run: uv sync
      - name: Run Codeflash Optimizer
        working-directory: ./src/backend/base
        continue-on-error: true
        run: uv run codeflash

