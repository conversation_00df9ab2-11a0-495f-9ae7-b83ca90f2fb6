#!/bin/bash

# Langflow API Test Script using curl
# This script tests basic API endpoints using curl commands

 LANGFLOW_SERVER_URL="http://localhost:7860/api"
LANGFLOW_API_KEY="sk-63ENbbTpgcNsBz1pcOEuvY2Qd4688GqZLqIRzxR15KA"

echo "🚀 Testing Langflow API with curl"
echo "================================"

# Test 1: Get version
echo -e "\n1. Testing /v1/version endpoint..."
curl -s -X GET \
  "$LANGFLOW_SERVER_URL/v1/version" \
  -H "accept: application/json" \
  -H "x-api-key: $LANGFLOW_API_KEY" | jq .

# Test 2: Get configuration
echo -e "\n2. Testing /v1/config endpoint..."
curl -s -X GET \
  "$LANGFLOW_SERVER_URL/v1/config" \
  -H "accept: application/json" \
  -H "x-api-key: $LANGFLOW_API_KEY" | jq .

# Test 3: Get all components
echo -e "\n3. Testing /v1/all endpoint..."
curl -s -X GET \
  "$LANGFLOW_SERVER_URL/v1/all" \
  -H "accept: application/json" \
  -H "x-api-key: $LANGFLOW_API_KEY" | jq 'length' | xargs echo "Number of components:"

# Test 4: Get flows
echo -e "\n4. Testing /v1/flows endpoint..."
curl -s -X GET \
  "$LANGFLOW_SERVER_URL/v1/flows/?remove_example_flows=false&components_only=false&get_all=true&header_flows=false&page=1&size=50" \
  -H "accept: application/json" \
  -H "x-api-key: $LANGFLOW_API_KEY" | jq '. | length' | xargs echo "Number of flows:"

echo -e "\n✅ All curl tests completed!"