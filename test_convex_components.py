"""
Test suite for Convex Pica components
"""

import json
import pytest
from unittest.mock import AsyncMock, patch, MagicMock

# Import the components (adjust path as needed)
from convex_pica_component import ConvexPicaComponent
from convex_schema_component import ConvexSchemaComponent


class TestConvexPicaComponent:
    """Test cases for the basic Convex Pica component."""

    @pytest.fixture
    def component(self):
        """Create a test component instance."""
        return ConvexPicaComponent(
            pica_api_key="test-api-key",
            pica_connection_key="live::convex::default::test123",
            operation_type="query",
            function_path="messages/list",
            function_args='{"limit": 10}',
            include_logs=True
        )

    def test_component_initialization(self, component):
        """Test component initializes correctly."""
        assert component.pica_api_key == "test-api-key"
        assert component.operation_type == "query"
        assert component.function_path == "messages/list"
        assert component.include_logs is True

    def test_parse_function_args_valid_json(self, component):
        """Test parsing valid JSON function arguments."""
        component.function_args = '{"limit": 10, "filter": "active"}'
        args = component._parse_function_args()
        assert args == {"limit": 10, "filter": "active"}

    def test_parse_function_args_empty(self, component):
        """Test parsing empty function arguments."""
        component.function_args = ""
        args = component._parse_function_args()
        assert args == {}

    def test_parse_function_args_invalid_json(self, component):
        """Test parsing invalid JSON raises error."""
        component.function_args = '{"invalid": json}'
        with pytest.raises(ValueError, match="Invalid JSON"):
            component._parse_function_args()

    def test_get_action_id_for_operation(self, component):
        """Test getting correct action IDs for different operations."""
        component.operation_type = "query"
        action_id = component._get_action_id_for_operation()
        assert action_id == "conn_mod_def::GC-4oMx93W0::b73lKTyLQjOaCct8g4XBpg"

        component.operation_type = "mutation"
        action_id = component._get_action_id_for_operation()
        assert action_id == "conn_mod_def::GC-4pKAk_EA::0Dl8rEahS3KB7V31-rULgw"

    def test_get_api_path_for_operation(self, component):
        """Test getting correct API paths for different operations."""
        component.operation_type = "query"
        path = component._get_api_path_for_operation()
        assert path == "/api/query"

        component.operation_type = "run_function"
        component.function_path = "messages/list"
        path = component._get_api_path_for_operation()
        assert path == "/api/run/messages/list"

    def test_validate_inputs_valid(self, component):
        """Test input validation with valid inputs."""
        errors = component.validate_inputs()
        assert len(errors) == 0

    def test_validate_inputs_missing_api_key(self, component):
        """Test input validation with missing API key."""
        component.pica_api_key = ""
        errors = component.validate_inputs()
        assert "Pica API key is required" in errors

    def test_validate_inputs_invalid_connection_key(self, component):
        """Test input validation with invalid connection key."""
        component.pica_connection_key = "invalid-key-format"
        errors = component.validate_inputs()
        assert any("Invalid Pica connection key format" in error for error in errors)

    @pytest.mark.asyncio
    @patch('httpx.AsyncClient.post')
    async def test_make_pica_request_success(self, mock_post, component):
        """Test successful Pica API request."""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "status": "success",
            "value": [{"id": "1", "text": "Hello"}],
            "logLines": ["Query executed"]
        }
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response

        result = await component._make_pica_request(
            action_id="test-action",
            method="POST",
            path="/api/query",
            data={"test": "data"}
        )

        assert result["status"] == "success"
        assert len(result["value"]) == 1
        mock_post.assert_called_once()

    @pytest.mark.asyncio
    @patch('httpx.AsyncClient.post')
    async def test_execute_convex_operation_success(self, mock_post, component):
        """Test successful Convex operation execution."""
        # Mock successful Pica response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "status": "success",
            "value": [{"id": "1", "text": "Hello"}],
            "logLines": ["Query executed successfully"]
        }
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response

        result = await component.execute_convex_operation()

        assert result.data["success"] is True
        assert result.data["operation"] == "query"
        assert result.data["function_path"] == "messages/list"
        assert "logs" in result.data

    @pytest.mark.asyncio
    @patch('httpx.AsyncClient.post')
    async def test_execute_convex_operation_error(self, mock_post, component):
        """Test Convex operation with error response."""
        # Mock error response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "status": "error",
            "errorMessage": "Function not found",
            "errorData": {"code": "FUNCTION_NOT_FOUND"},
            "logLines": ["Error: Function not found"]
        }
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response

        result = await component.execute_convex_operation()

        assert result.data["success"] is False
        assert "Function not found" in result.data["error"]
        assert result.data["operation"] == "query"

    def test_get_raw_response_no_operation(self, component):
        """Test getting raw response when no operation has been executed."""
        response = component.get_raw_response()
        assert "No operation has been executed yet" in response.text

    def test_get_example_usage(self):
        """Test getting example usage documentation."""
        examples = ConvexPicaComponent.get_example_usage()
        assert "query_example" in examples
        assert "mutation_example" in examples
        assert "action_example" in examples


class TestConvexSchemaComponent:
    """Test cases for the Convex Schema component."""

    @pytest.fixture
    def schema_component(self):
        """Create a test schema component instance."""
        return ConvexSchemaComponent(
            pica_api_key="test-api-key",
            pica_connection_key="live::convex::default::test123",
            schema_operation="list_schemas",
            include_metadata=True,
            delta_schema=False,
            custom_params="{}"
        )

    def test_schema_component_initialization(self, schema_component):
        """Test schema component initializes correctly."""
        assert schema_component.schema_operation == "list_schemas"
        assert schema_component.include_metadata is True
        assert schema_component.delta_schema is False

    def test_get_schema_action_mapping(self, schema_component):
        """Test getting schema action mapping."""
        mapping = schema_component._get_schema_action_mapping()
        assert "list_schemas" in mapping
        assert "clear_tables" in mapping
        assert mapping["list_schemas"]["method"] == "GET"
        assert mapping["clear_tables"]["method"] == "PUT"

    def test_prepare_operation_params_list_schemas(self, schema_component):
        """Test preparing parameters for list_schemas operation."""
        params = schema_component._prepare_operation_params()
        assert params["deltaSchema"] is False
        assert params["format"] == "json"

    def test_prepare_operation_params_clear_tables(self, schema_component):
        """Test preparing parameters for clear_tables operation."""
        schema_component.schema_operation = "clear_tables"
        schema_component.table_names = "messages,users,channels"
        
        params = schema_component._prepare_operation_params()
        assert "tableNames" in params
        assert params["tableNames"] == ["messages", "users", "channels"]

    def test_prepare_operation_params_clear_tables_no_names(self, schema_component):
        """Test clear_tables operation without table names raises error."""
        schema_component.schema_operation = "clear_tables"
        schema_component.table_names = ""
        
        with pytest.raises(ValueError, match="Table names are required"):
            schema_component._prepare_operation_params()

    @pytest.mark.asyncio
    @patch('httpx.AsyncClient.post')
    async def test_execute_schema_operation_success(self, mock_post, schema_component):
        """Test successful schema operation execution."""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "data": {
                "messages": {
                    "type": "object",
                    "properties": {
                        "_id": {"type": "string"},
                        "text": {"type": "string"}
                    }
                }
            }
        }
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response

        result = await schema_component.execute_schema_operation()

        assert result.data["success"] is True
        assert result.data["operation"] == "list_schemas"
        assert "messages" in result.data["data"]

    def test_get_table_info_with_schema_data(self, schema_component):
        """Test extracting table information from schema response."""
        schema_component._last_response = {
            "data": {
                "messages": {
                    "type": "object",
                    "properties": {
                        "_id": {"type": "string"},
                        "text": {"type": "string"},
                        "author": {"type": "string"}
                    }
                },
                "users": {
                    "type": "object", 
                    "properties": {
                        "_id": {"type": "string"},
                        "name": {"type": "string"}
                    }
                }
            }
        }
        schema_component.schema_operation = "list_schemas"

        table_info = schema_component.get_table_info()
        
        assert "Table: messages" in table_info.text
        assert "Fields: 3" in table_info.text
        assert "Table: users" in table_info.text
        assert "Fields: 2" in table_info.text

    def test_get_operation_status_no_operation(self, schema_component):
        """Test getting operation status when no operation has been executed."""
        status = schema_component.get_operation_status()
        assert "No operation has been executed yet" in status.text


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
